{"version": 3, "file": "info.js", "sources": ["pages/info/info.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5mby9pbmZvLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"info-center-container\">\n    <!-- 状态栏安全区域 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n    \n    <!-- 自定义头部 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"page-title\">信息中心</text>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" color=\"#fff\" size=\"44\" @click=\"handleSearch\" />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 用户状态提示 -->\n    <UserStatusBanner :showAction=\"true\" />\n    \n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- 轮播公告 -->\n      <view v-if=\"bannerList.length > 0\" class=\"banner-section\">\n        <swiper \n          class=\"banner-swiper\" \n          :indicator-dots=\"bannerList.length > 1\"\n          :autoplay=\"true\"\n          :interval=\"5000\"\n          :duration=\"500\"\n          circular\n        >\n          <swiper-item v-for=\"banner in bannerList\" :key=\"banner.id\">\n            <view class=\"banner-item\" @click=\"viewDetail(banner)\">\n              <view class=\"banner-content\">\n                <text class=\"banner-title\">{{ banner.title }}</text>\n                <text class=\"banner-desc\">{{ banner.content.substring(0, 50) }}...</text>\n              </view>\n              <view v-if=\"banner.isImportant\" class=\"banner-tag important\">重要</view>\n            </view>\n          </swiper-item>\n        </swiper>\n      </view>\n      \n      <!-- 内容分类 -->\n      <view class=\"content-categories\">\n        <u-tabs \n          v-model=\"currentTab\" \n          :list=\"tabList\" \n          @change=\"onTabChange\"\n          activeColor=\"#2E8B57\"\n          inactiveColor=\"#666\"\n          lineColor=\"#2E8B57\"\n          :lineWidth=\"60\"\n          :lineHeight=\"6\"\n        />\n      </view>\n      \n      <!-- 内容列表 -->\n      <view class=\"content-list\">\n        <scroll-view \n          class=\"list-scroll\"\n          scroll-y\n          :refresher-enabled=\"true\"\n          :refresher-triggered=\"isRefreshing\"\n          @refresherrefresh=\"onRefresh\"\n          @scrolltolower=\"onLoadMore\"\n        >\n          <!-- 空状态 -->\n          <EmptyState \n            v-if=\"currentList.length === 0 && !isLoading\"\n            type=\"no-data\"\n            title=\"暂无内容\"\n            description=\"当前分类下暂时没有内容\"\n            :showButton=\"true\"\n            buttonText=\"刷新\"\n            @buttonClick=\"onRefresh\"\n          />\n          \n          <!-- 内容列表 -->\n          <view v-else>\n            <view \n              v-for=\"item in currentList\" \n              :key=\"item.id\" \n              class=\"content-item\"\n              @click=\"viewDetail(item)\"\n            >\n              <view class=\"item-header\">\n                <text class=\"item-title\">{{ item.title }}</text>\n                <view v-if=\"item.isImportant\" class=\"important-tag\">\n                  <text>重要</text>\n                </view>\n                <view v-if=\"item.isTop\" class=\"top-tag\">\n                  <text>置顶</text>\n                </view>\n              </view>\n              \n              <text v-if=\"item.content\" class=\"item-summary\">{{ item.content.substring(0, 100) }}...</text>\n              \n              <view class=\"item-footer\">\n                <text class=\"item-date\">{{ formatRelativeTime(item.publishTime) }}</text>\n                <view class=\"item-meta\">\n                  <text class=\"item-source\" v-if=\"item.source\">{{ item.source }}</text>\n                  <StatusTag :type=\"'announcement'\" :status=\"item.type\" />\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 加载更多提示 -->\n          <view v-if=\"isLoading\" class=\"loading-more\">\n            <LoadingSpinner size=\"small\" text=\"加载中...\" />\n          </view>\n          \n          <view v-if=\"!hasMore && currentList.length > 0\" class=\"no-more\">\n            <text>没有更多内容了</text>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useUserStore } from '../../src/stores/user'\nimport { useAppStore } from '../../src/stores/app'\nimport api from '../../src/api'\nimport { PAGE_PATHS } from '../../src/constants'\nimport { formatRelativeTime } from '../../src/utils'\nimport type { Announcement, PaginationParams } from '../../src/types'\n\n// 导入组件\nimport UserStatusBanner from '../../src/components/common/UserStatusBanner.vue'\nimport EmptyState from '../../src/components/common/EmptyState.vue'\nimport LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'\nimport StatusTag from '../../src/components/common/StatusTag.vue'\n\n// Store\nconst userStore = useUserStore()\nconst appStore = useAppStore()\n\n// 系统信息\nconst statusBarHeight = ref(0)\n\n// 数据状态\nconst currentTab = ref(0)\nconst isLoading = ref(false)\nconst isRefreshing = ref(false)\nconst hasMore = ref(true)\nconst currentPage = ref(1)\n\n// 数据列表\nconst bannerList = ref<Announcement[]>([])\nconst allList = ref<Announcement[]>([])\n\n// Tab配置\nconst tabList = [\n  { name: '全部', key: 'all' },\n  { name: '公告', key: 'notice' },\n  { name: '政策法规', key: 'policy' },\n  { name: '重要通知', key: 'news' }\n]\n\n// 计算当前列表\nconst currentList = computed(() => {\n  const currentTabKey = tabList[currentTab.value].key\n  if (currentTabKey === 'all') {\n    return allList.value\n  }\n  return allList.value.filter(item => item.type === currentTabKey)\n})\n\nonMounted(() => {\n  // 获取系统信息\n  const systemInfo = uni.getSystemInfoSync()\n  statusBarHeight.value = systemInfo.statusBarHeight || 0\n  \n  // 初始化数据\n  loadData()\n  loadBanners()\n})\n\n// 切换Tab\nconst onTabChange = (index: number) => {\n  currentTab.value = index\n  currentPage.value = 1\n  hasMore.value = true\n  allList.value = []\n  loadData()\n}\n\n// 加载轮播数据\nconst loadBanners = async () => {\n  try {\n    const response = await api.info.getAnnouncements({\n      page: 1,\n      pageSize: 3,\n      type: 'notice'\n    })\n    \n    bannerList.value = response.data.list.filter(item => item.isImportant)\n  } catch (error) {\n    uni.__f__('error','at pages/info/info.vue:202','加载轮播数据失败:', error)\n  }\n}\n\n// 加载数据\nconst loadData = async () => {\n  if (isLoading.value) return\n  \n  isLoading.value = true\n  \n  try {\n    const params: PaginationParams & { type?: string } = {\n      page: currentPage.value,\n      pageSize: 10\n    }\n    \n    const currentTabKey = tabList[currentTab.value].key\n    if (currentTabKey !== 'all') {\n      params.type = currentTabKey\n    }\n    \n    const response = await api.info.getAnnouncements(params)\n    \n    if (currentPage.value === 1) {\n      allList.value = response.data.list\n    } else {\n      allList.value.push(...response.data.list)\n    }\n    \n    hasMore.value = response.data.page < response.data.totalPages\n  } catch (error: any) {\n    uni.__f__('error','at pages/info/info.vue:233','加载数据失败:', error)\n    appStore.showToast(error.message || '加载失败，请重试')\n  } finally {\n    isLoading.value = false\n    isRefreshing.value = false\n  }\n}\n\n// 下拉刷新\nconst onRefresh = () => {\n  isRefreshing.value = true\n  currentPage.value = 1\n  hasMore.value = true\n  allList.value = []\n  loadData()\n  loadBanners()\n}\n\n// 加载更多\nconst onLoadMore = () => {\n  if (hasMore.value && !isLoading.value) {\n    currentPage.value++\n    loadData()\n  }\n}\n\n// 查看详情\nconst viewDetail = (item: Announcement) => {\n  appStore.navigateTo(PAGE_PATHS.INFO_DETAIL, { id: item.id })\n}\n\n// 搜索\nconst handleSearch = () => {\n  appStore.showToast('搜索功能开发中')\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.info-center-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n}\n\n.status-bar {\n  background: linear-gradient(135deg, $acdc-primary 0%, $acdc-primary-dark 100%);\n}\n\n.header {\n  background: linear-gradient(135deg, $acdc-primary 0%, $acdc-primary-dark 100%);\n  padding: 20rpx 30rpx 40rpx;\n  \n  .header-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    \n    .page-title {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #fff;\n    }\n    \n    .header-actions {\n      display: flex;\n      align-items: center;\n    }\n  }\n}\n\n.main-content {\n  padding-bottom: 120rpx; // 为底部导航留空间\n}\n\n.banner-section {\n  margin: 30rpx;\n  \n  .banner-swiper {\n    height: 200rpx;\n    border-radius: 24rpx;\n    overflow: hidden;\n    \n    .banner-item {\n      position: relative;\n      height: 100%;\n      background: linear-gradient(135deg, $acdc-primary 0%, $acdc-primary-light 100%);\n      padding: 40rpx;\n      display: flex;\n      align-items: center;\n      \n      .banner-content {\n        flex: 1;\n        \n        .banner-title {\n          display: block;\n          font-size: 30rpx;\n          font-weight: bold;\n          color: #fff;\n          margin-bottom: 16rpx;\n          line-height: 1.4;\n        }\n        \n        .banner-desc {\n          font-size: 24rpx;\n          color: rgba(255, 255, 255, 0.8);\n          line-height: 1.4;\n        }\n      }\n      \n      .banner-tag {\n        position: absolute;\n        top: 20rpx;\n        right: 20rpx;\n        padding: 8rpx 16rpx;\n        background: rgba(255, 149, 0, 0.9);\n        border-radius: 12rpx;\n        font-size: 20rpx;\n        color: #fff;\n        \n        &.important {\n          background: rgba(245, 34, 45, 0.9);\n        }\n      }\n    }\n  }\n}\n\n.content-categories {\n  background: #fff;\n  padding: 0 30rpx;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.content-list {\n  .list-scroll {\n    height: calc(100vh - 400rpx);\n  }\n  \n  .content-item {\n    margin: 20rpx 30rpx;\n    padding: 40rpx;\n    background: #fff;\n    border-radius: 24rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n    \n    .item-header {\n      display: flex;\n      align-items: flex-start;\n      justify-content: space-between;\n      margin-bottom: 20rpx;\n      gap: 16rpx;\n      \n      .item-title {\n        flex: 1;\n        font-size: 30rpx;\n        font-weight: bold;\n        color: $acdc-text-primary;\n        line-height: 1.4;\n      }\n      \n      .important-tag {\n        padding: 8rpx 16rpx;\n        background: #fff3e0;\n        border-radius: 12rpx;\n        border: 2rpx solid $uni-color-warning;\n        \n        text {\n          font-size: 20rpx;\n          color: $uni-color-warning;\n          font-weight: bold;\n        }\n      }\n      \n      .top-tag {\n        padding: 8rpx 16rpx;\n        background: #f6ffed;\n        border-radius: 12rpx;\n        border: 2rpx solid $uni-color-success;\n        \n        text {\n          font-size: 20rpx;\n          color: $uni-color-success;\n          font-weight: bold;\n        }\n      }\n    }\n    \n    .item-summary {\n      display: block;\n      font-size: 26rpx;\n      color: $acdc-text-secondary;\n      line-height: 1.5;\n      margin-bottom: 24rpx;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      display: -webkit-box;\n      -webkit-line-clamp: 2;\n      -webkit-box-orient: vertical;\n    }\n    \n    .item-footer {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      \n      .item-date {\n        font-size: 24rpx;\n        color: $acdc-text-disabled;\n      }\n      \n      .item-meta {\n        display: flex;\n        align-items: center;\n        gap: 16rpx;\n        \n        .item-source {\n          font-size: 24rpx;\n          color: $acdc-text-disabled;\n        }\n      }\n    }\n  }\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.no-more {\n  text-align: center;\n  padding: 40rpx;\n  color: $acdc-text-disabled;\n  font-size: 26rpx;\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/info/info.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "ref", "computed", "onMounted", "uni", "api", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA,MAAA,mBAA6B,MAAA;AAC7B,MAAA,aAAuB,MAAA;AACvB,MAAA,iBAA2B,MAAA;AAC3B,MAAA,YAAsB,MAAA;;;;AAGJA,iCAAa;AAC/B,UAAM,WAAWC,eAAAA;AAGX,UAAA,kBAAkBC,kBAAI,CAAC;AAGvB,UAAA,aAAaA,kBAAI,CAAC;AAClB,UAAA,YAAYA,kBAAI,KAAK;AACrB,UAAA,eAAeA,kBAAI,KAAK;AACxB,UAAA,UAAUA,kBAAI,IAAI;AAClB,UAAA,cAAcA,kBAAI,CAAC;AAGnB,UAAA,aAAaA,kBAAoB,CAAA,CAAE;AACnC,UAAA,UAAUA,kBAAoB,CAAA,CAAE;AAGtC,UAAM,UAAU;AAAA,MACd,EAAE,MAAM,MAAM,KAAK,MAAM;AAAA,MACzB,EAAE,MAAM,MAAM,KAAK,SAAS;AAAA,MAC5B,EAAE,MAAM,QAAQ,KAAK,SAAS;AAAA,MAC9B,EAAE,MAAM,QAAQ,KAAK,OAAO;AAAA,IAAA;AAIxB,UAAA,cAAcC,cAAAA,SAAS,MAAM;AACjC,YAAM,gBAAgB,QAAQ,WAAW,KAAK,EAAE;AAChD,UAAI,kBAAkB,OAAO;AAC3B,eAAO,QAAQ;AAAA,MACjB;AACA,aAAO,QAAQ,MAAM,OAAO,CAAQ,SAAA,KAAK,SAAS,aAAa;AAAA,IAAA,CAChE;AAEDC,kBAAAA,UAAU,MAAM;AAER,YAAA,aAAaC,oBAAI;AACP,sBAAA,QAAQ,WAAW,mBAAmB;AAG7C;AACG;IAAA,CACb;AAGK,UAAA,cAAc,CAAC,UAAkB;AACrC,iBAAW,QAAQ;AACnB,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB,cAAQ,QAAQ;AACP;IAAA;AAIX,UAAM,cAAc,MAAY;AAC1B,UAAA;AACF,cAAM,WAAW,MAAMC,kBAAI,KAAK,iBAAiB;AAAA,UAC/C,MAAM;AAAA,UACN,UAAU;AAAA,UACV,MAAM;AAAA,QAAA,CACP;AAED,mBAAW,QAAQ,SAAS,KAAK,KAAK,OAAO,CAAA,SAAQ,KAAK,WAAW;AAAA,eAC9D,OAAO;AACdD,sBAAA,MAAI,MAAM,SAAQ,8BAA6B,aAAa,KAAK;AAAA,MACnE;AAAA,IAAA;AAIF,UAAM,WAAW,MAAY;AAC3B,UAAI,UAAU;AAAO;AAErB,gBAAU,QAAQ;AAEd,UAAA;AACF,cAAM,SAA+C;AAAA,UACnD,MAAM,YAAY;AAAA,UAClB,UAAU;AAAA,QAAA;AAGZ,cAAM,gBAAgB,QAAQ,WAAW,KAAK,EAAE;AAChD,YAAI,kBAAkB,OAAO;AAC3B,iBAAO,OAAO;AAAA,QAChB;AAEA,cAAM,WAAW,MAAMC,cAAI,IAAA,KAAK,iBAAiB,MAAM;AAEnD,YAAA,YAAY,UAAU,GAAG;AACnB,kBAAA,QAAQ,SAAS,KAAK;AAAA,QAAA,OACzB;AACL,kBAAQ,MAAM,KAAK,GAAG,SAAS,KAAK,IAAI;AAAA,QAC1C;AAEA,gBAAQ,QAAQ,SAAS,KAAK,OAAO,SAAS,KAAK;AAAA,eAC5C,OAAY;AACnBD,sBAAA,MAAI,MAAM,SAAQ,8BAA6B,WAAW,KAAK;AACtD,iBAAA,UAAU,MAAM,WAAW,UAAU;AAAA,MAAA,UAC9C;AACA,kBAAU,QAAQ;AAClB,qBAAa,QAAQ;AAAA,MACvB;AAAA,IAAA;AAIF,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB,cAAQ,QAAQ;AACP;AACG;IAAA;AAId,UAAM,aAAa,MAAM;AACvB,UAAI,QAAQ,SAAS,CAAC,UAAU,OAAO;AACzB,oBAAA;AACH;MACX;AAAA,IAAA;AAII,UAAA,aAAa,CAAC,SAAuB;AACzC,eAAS,WAAWE,oBAAAA,WAAW,aAAa,EAAE,IAAI,KAAK,IAAI;AAAA,IAAA;AAI7D,UAAM,eAAe,MAAM;AACzB,eAAS,UAAU,SAAS;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxQ9B,GAAG,WAAW,eAAe;"}