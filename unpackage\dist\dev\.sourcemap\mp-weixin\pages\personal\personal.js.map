{"version": 3, "file": "personal.js", "sources": ["pages/personal/personal.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGVyc29uYWwvcGVyc29uYWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"personal-container\">\n    <!-- 状态栏安全区域 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n    \n    <!-- 用户信息头部 -->\n    <view class=\"user-header\">\n      <view class=\"header-bg\"></view>\n      <view class=\"user-info\">\n        <view class=\"avatar-section\">\n          <image \n            :src=\"userInfo?.photo || '/static/images/default-avatar.png'\" \n            class=\"user-avatar\"\n            mode=\"aspectFill\"\n            @click=\"viewAvatar\"\n          />\n          <view v-if=\"userInfo?.status\" class=\"status-badge\" :class=\"userInfo.status\">\n            <text>{{ getUserStatusText() }}</text>\n          </view>\n        </view>\n        \n        <view class=\"info-section\">\n          <text class=\"user-name\">{{ userInfo?.realName || '未设置' }}</text>\n          <text class=\"user-org\">{{ userInfo?.organization || '未设置机构' }}</text>\n          <view class=\"user-meta\">\n            <text class=\"join-date\">注册时间：{{ formatDate(userInfo?.createdAt, 'YYYY-MM-DD') }}</text>\n          </view>\n        </view>\n        \n        <view class=\"action-section\">\n          <u-icon name=\"edit-pen\" color=\"#fff\" size=\"32\" @click=\"editProfile\" />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 用户状态提示 -->\n    <UserStatusBanner :showAction=\"true\" />\n    \n    <!-- 统计数据 -->\n    <view class=\"stats-section\">\n      <view class=\"stats-card\">\n        <view class=\"stat-item\" @click=\"viewStudyStats\">\n          <text class=\"stat-number\">{{ studyStats.totalSessions }}</text>\n          <text class=\"stat-label\">学习次数</text>\n        </view>\n        <view class=\"stat-divider\"></view>\n        <view class=\"stat-item\" @click=\"viewExamStats\">\n          <text class=\"stat-number\">{{ examStats.total }}</text>\n          <text class=\"stat-label\">参加考试</text>\n        </view>\n        <view class=\"stat-divider\"></view>\n        <view class=\"stat-item\" @click=\"viewCertificates\">\n          <text class=\"stat-number\">{{ certificateStats.total }}</text>\n          <text class=\"stat-label\">获得证书</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 功能菜单 -->\n    <view class=\"menu-section\">\n      <!-- 个人信息管理 -->\n      <view class=\"menu-group\">\n        <view class=\"group-title\">\n          <text>个人信息</text>\n        </view>\n        <view class=\"menu-list\">\n          <view class=\"menu-item\" @click=\"viewPersonalInfo\">\n            <view class=\"menu-icon\">\n              <u-icon name=\"account\" color=\"#4A90E2\" size=\"40\" />\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">个人资料</text>\n              <text class=\"menu-desc\">查看和编辑个人信息</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n          \n          <view class=\"menu-item\" @click=\"viewCertificates\">\n            <view class=\"menu-icon\">\n              <u-icon name=\"medal\" color=\"#FFD700\" size=\"40\" />\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">我的证书</text>\n              <text class=\"menu-desc\">查看已获得的证书</text>\n            </view>\n            <view class=\"menu-badge\" v-if=\"certificateStats.pending > 0\">\n              <text>{{ certificateStats.pending }}</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n        </view>\n      </view>\n      \n      <!-- 学习记录 -->\n      <view class=\"menu-group\">\n        <view class=\"group-title\">\n          <text>学习记录</text>\n        </view>\n        <view class=\"menu-list\">\n          <view class=\"menu-item\" @click=\"viewStudyHistory\">\n            <view class=\"menu-icon\">\n              <u-icon name=\"book\" color=\"#4CAF50\" size=\"40\" />\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">学习历史</text>\n              <text class=\"menu-desc\">查看练习记录和成绩</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n          \n          <view class=\"menu-item\" @click=\"viewExamHistory\">\n            <view class=\"menu-icon\">\n              <u-icon name=\"file-text\" color=\"#FF9500\" size=\"40\" />\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">考试记录</text>\n              <text class=\"menu-desc\">查看考试历史和成绩</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n        </view>\n      </view>\n      \n      <!-- 系统设置 -->\n      <view class=\"menu-group\">\n        <view class=\"group-title\">\n          <text>系统设置</text>\n        </view>\n        <view class=\"menu-list\">\n          <view class=\"menu-item\" @click=\"submitFeedback\">\n            <view class=\"menu-icon\">\n              <u-icon name=\"chat\" color=\"#9C27B0\" size=\"40\" />\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">意见反馈</text>\n              <text class=\"menu-desc\">提交问题和建议</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n          \n          <view class=\"menu-item\" @click=\"viewAbout\">\n            <view class=\"menu-icon\">\n              <u-icon name=\"info-circle\" color=\"#607D8B\" size=\"40\" />\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">关于我们</text>\n              <text class=\"menu-desc\">版本信息和联系方式</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n          \n          <view class=\"menu-item\" @click=\"logout\">\n            <view class=\"menu-icon\">\n              <u-icon name=\"logout\" color=\"#f56c6c\" size=\"40\" />\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">退出登录</text>\n              <text class=\"menu-desc\">安全退出当前账号</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { onShow } from '@dcloudio/uni-app'\nimport { useUserStore } from '../../src/stores/user'\nimport { useAppStore } from '../../src/stores/app'\nimport { useStudyStore } from '../../src/stores/study'\nimport { formatDate } from '../../src/utils'\nimport { PAGE_PATHS, USER_STATUS_TEXT } from '../../src/constants'\n\n// 导入组件\nimport UserStatusBanner from '../../src/components/common/UserStatusBanner.vue'\n\n// Store\nconst userStore = useUserStore()\nconst appStore = useAppStore()\nconst studyStore = useStudyStore()\n\n// 系统信息\nconst statusBarHeight = ref(0)\n\n// 统计数据\nconst studyStats = ref({\n  totalSessions: 0,\n  totalQuestions: 0,\n  accuracy: 0\n})\n\nconst examStats = ref({\n  total: 0,\n  passed: 0,\n  pending: 0\n})\n\nconst certificateStats = ref({\n  total: 0,\n  pending: 0,\n  active: 0\n})\n\n// 计算属性\nconst userInfo = computed(() => userStore.userInfo)\n\nonMounted(() => {\n  // 获取系统信息\n  const systemInfo = uni.getSystemInfoSync()\n  statusBarHeight.value = systemInfo.statusBarHeight || 0\n  \n  // 加载统计数据\n  loadStats()\n})\n\nonShow(() => {\n  // 页面显示时刷新数据\n  loadStats()\n})\n\n// 加载统计数据\nconst loadStats = () => {\n  // 学习统计\n  studyStats.value = studyStore.getSessionStats()\n  \n  // 考试统计（这里可以从API获取）\n  examStats.value = {\n    total: 12,\n    passed: 10,\n    pending: 2\n  }\n  \n  // 证书统计（这里可以从API获取）\n  certificateStats.value = {\n    total: 8,\n    pending: 1,\n    active: 7\n  }\n}\n\n// 获取用户状态文本\nconst getUserStatusText = () => {\n  if (!userInfo.value?.status) return ''\n  return USER_STATUS_TEXT[userInfo.value.status] || userInfo.value.status\n}\n\n// 查看头像\nconst viewAvatar = () => {\n  if (userInfo.value?.photo) {\n    uni.previewImage({\n      urls: [userInfo.value.photo],\n      current: userInfo.value.photo\n    })\n  }\n}\n\n// 编辑个人资料\nconst editProfile = () => {\n  if (userInfo.value?.status === 'not_submitted') {\n    appStore.redirectTo(PAGE_PATHS.PROFILE)\n  } else if (userInfo.value?.status === 'rejected') {\n    appStore.redirectTo(PAGE_PATHS.PROFILE)\n  } else {\n    appStore.navigateTo(PAGE_PATHS.PERSONAL_INFO)\n  }\n}\n\n// 查看个人信息\nconst viewPersonalInfo = () => {\n  appStore.navigateTo(PAGE_PATHS.PERSONAL_INFO)\n}\n\n// 查看证书\nconst viewCertificates = () => {\n  appStore.navigateTo(PAGE_PATHS.PERSONAL_CERTIFICATE)\n}\n\n// 查看学习统计\nconst viewStudyStats = () => {\n  appStore.showToast('学习统计功能开发中')\n}\n\n// 查看考试统计\nconst viewExamStats = () => {\n  appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY)\n}\n\n// 查看学习历史\nconst viewStudyHistory = () => {\n  appStore.showToast('学习历史功能开发中')\n}\n\n// 查看考试历史\nconst viewExamHistory = () => {\n  appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY)\n}\n\n// 提交反馈\nconst submitFeedback = () => {\n  appStore.navigateTo(PAGE_PATHS.PERSONAL_FEEDBACK)\n}\n\n// 关于我们\nconst viewAbout = () => {\n  appStore.navigateTo(PAGE_PATHS.PERSONAL_ABOUT)\n}\n\n// 退出登录\nconst logout = () => {\n  appStore.showModal({\n    title: '确认退出',\n    content: '确定要退出登录吗？',\n    confirmText: '确认退出',\n    cancelText: '取消'\n  }).then((confirmed) => {\n    if (confirmed) {\n      userStore.logout()\n      appStore.reLaunch(PAGE_PATHS.LOGIN)\n    }\n  })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.personal-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n}\n\n.status-bar {\n  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);\n}\n\n.user-header {\n  position: relative;\n  padding-bottom: 40rpx;\n  \n  .header-bg {\n    height: 200rpx;\n    background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);\n  }\n  \n  .user-info {\n    position: absolute;\n    bottom: 0;\n    left: 30rpx;\n    right: 30rpx;\n    background: #fff;\n    border-radius: 24rpx;\n    padding: 40rpx;\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    \n    .avatar-section {\n      position: relative;\n      margin-right: 32rpx;\n      \n      .user-avatar {\n        width: 120rpx;\n        height: 120rpx;\n        border-radius: 60rpx;\n        border: 4rpx solid #2E8B57;\n      }\n      \n      .status-badge {\n        position: absolute;\n        bottom: -8rpx;\n        right: -8rpx;\n        padding: 4rpx 12rpx;\n        border-radius: 12rpx;\n        font-size: 18rpx;\n        color: #fff;\n        \n        &.not_submitted {\n          background: #FF9500;\n        }\n        \n        &.pending {\n          background: #4A90E2;\n        }\n        \n        &.approved {\n          background: #4CAF50;\n        }\n        \n        &.rejected {\n          background: #f56c6c;\n        }\n      }\n    }\n    \n    .info-section {\n      flex: 1;\n      \n      .user-name {\n        display: block;\n        font-size: 32rpx;\n        font-weight: bold;\n        color: #333;\n        margin-bottom: 8rpx;\n      }\n      \n      .user-org {\n        display: block;\n        font-size: 26rpx;\n        color: #666;\n        margin-bottom: 8rpx;\n      }\n      \n      .user-meta {\n        .join-date {\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n    }\n    \n    .action-section {\n      width: 60rpx;\n      height: 60rpx;\n      background: #2E8B57;\n      border-radius: 30rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n}\n\n.stats-section {\n  margin: 40rpx 30rpx;\n  \n  .stats-card {\n    background: #fff;\n    border-radius: 24rpx;\n    padding: 40rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n    display: flex;\n    align-items: center;\n    \n    .stat-item {\n      flex: 1;\n      text-align: center;\n      \n      .stat-number {\n        display: block;\n        font-size: 48rpx;\n        font-weight: bold;\n        color: #2E8B57;\n        margin-bottom: 8rpx;\n      }\n      \n      .stat-label {\n        font-size: 24rpx;\n        color: #666;\n      }\n    }\n    \n    .stat-divider {\n      width: 2rpx;\n      height: 60rpx;\n      background: #f0f0f0;\n    }\n  }\n}\n\n.menu-section {\n  padding: 0 30rpx 120rpx;\n  \n  .menu-group {\n    margin-bottom: 40rpx;\n    \n    .group-title {\n      margin-bottom: 20rpx;\n      \n      text {\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333;\n      }\n    }\n    \n    .menu-list {\n      background: #fff;\n      border-radius: 24rpx;\n      overflow: hidden;\n      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n      \n      .menu-item {\n        display: flex;\n        align-items: center;\n        padding: 32rpx;\n        border-bottom: 2rpx solid #f8f9fa;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        .menu-icon {\n          width: 80rpx;\n          height: 80rpx;\n          background: rgba(74, 144, 226, 0.1);\n          border-radius: 20rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-right: 24rpx;\n        }\n        \n        .menu-content {\n          flex: 1;\n          \n          .menu-title {\n            display: block;\n            font-size: 30rpx;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 8rpx;\n          }\n          \n          .menu-desc {\n            font-size: 24rpx;\n            color: #666;\n          }\n        }\n        \n        .menu-badge {\n          width: 40rpx;\n          height: 40rpx;\n          background: #f56c6c;\n          border-radius: 20rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-right: 16rpx;\n          \n          text {\n            font-size: 20rpx;\n            color: #fff;\n            font-weight: bold;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/personal/personal.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "useStudyStore", "ref", "computed", "onMounted", "uni", "onShow", "USER_STATUS_TEXT", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;AAiLA,MAAA,mBAA6B,MAAA;;;;AAG7B,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AACjB,UAAM,aAAaC,iBAAAA;AAGb,UAAA,kBAAkBC,kBAAI,CAAC;AAG7B,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,UAAU;AAAA,IAAA,CACX;AAED,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACV;AAED,UAAM,mBAAmBA,cAAAA,IAAI;AAAA,MAC3B,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,IAAA,CACT;AAGD,UAAM,WAAWC,cAAA,SAAS,MAAM,UAAU,QAAQ;AAElDC,kBAAAA,UAAU,MAAM;AAER,YAAA,aAAaC,oBAAI;AACP,sBAAA,QAAQ,WAAW,mBAAmB;AAG5C;IAAA,CACX;AAEDC,kBAAAA,OAAO,MAAM;AAED;IAAA,CACX;AAGD,UAAM,YAAY,MAAM;AAEX,iBAAA,QAAQ,WAAW;AAG9B,gBAAU,QAAQ;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,MAAA;AAIX,uBAAiB,QAAQ;AAAA,QACvB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,MAAA;AAAA,IACV;AAIF,UAAM,oBAAoB,MAAM;;AAC1B,UAAA,GAAC,cAAS,UAAT,mBAAgB;AAAe,eAAA;AACpC,aAAOC,oBAAAA,iBAAiB,SAAS,MAAM,MAAM,KAAK,SAAS,MAAM;AAAA,IAAA;AAInE,UAAM,aAAa,MAAM;;AACnB,WAAA,cAAS,UAAT,mBAAgB,OAAO;AACzBF,sBAAAA,MAAI,aAAa;AAAA,UACf,MAAM,CAAC,SAAS,MAAM,KAAK;AAAA,UAC3B,SAAS,SAAS,MAAM;AAAA,QAAA,CACzB;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,cAAc,MAAM;;AACpB,YAAA,cAAS,UAAT,mBAAgB,YAAW,iBAAiB;AACrC,iBAAA,WAAWG,+BAAW,OAAO;AAAA,MAC7B,aAAA,cAAS,UAAT,mBAAgB,YAAW,YAAY;AACvC,iBAAA,WAAWA,+BAAW,OAAO;AAAA,MAAA,OACjC;AACI,iBAAA,WAAWA,+BAAW,aAAa;AAAA,MAC9C;AAAA,IAAA;AAIF,UAAM,mBAAmB,MAAM;AACpB,eAAA,WAAWA,+BAAW,aAAa;AAAA,IAAA;AAI9C,UAAM,mBAAmB,MAAM;AACpB,eAAA,WAAWA,+BAAW,oBAAoB;AAAA,IAAA;AAIrD,UAAM,iBAAiB,MAAM;AAC3B,eAAS,UAAU,WAAW;AAAA,IAAA;AAIhC,UAAM,gBAAgB,MAAM;AACjB,eAAA,WAAWA,+BAAW,YAAY;AAAA,IAAA;AAI7C,UAAM,mBAAmB,MAAM;AAC7B,eAAS,UAAU,WAAW;AAAA,IAAA;AAIhC,UAAM,kBAAkB,MAAM;AACnB,eAAA,WAAWA,+BAAW,YAAY;AAAA,IAAA;AAI7C,UAAM,iBAAiB,MAAM;AAClB,eAAA,WAAWA,+BAAW,iBAAiB;AAAA,IAAA;AAIlD,UAAM,YAAY,MAAM;AACb,eAAA,WAAWA,+BAAW,cAAc;AAAA,IAAA;AAI/C,UAAM,SAAS,MAAM;AACnB,eAAS,UAAU;AAAA,QACjB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,MAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,YAAI,WAAW;AACb,oBAAU,OAAO;AACR,mBAAA,SAASA,+BAAW,KAAK;AAAA,QACpC;AAAA,MAAA,CACD;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjUH,GAAG,WAAW,eAAe;"}