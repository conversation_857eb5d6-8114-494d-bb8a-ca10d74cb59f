/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-ab57d952 {
  box-sizing: border-box;
}
page.data-v-ab57d952 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-ab57d952 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-ab57d952 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-ab57d952 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-ab57d952 {
  display: flex;
}
.flex-column.data-v-ab57d952 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-ab57d952 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-ab57d952 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-ab57d952 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-ab57d952 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-ab57d952 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-ab57d952 {
  flex: 1;
}
.flex-wrap.data-v-ab57d952 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-ab57d952 {
  color: #262626;
}
.text-secondary.data-v-ab57d952 {
  color: #595959;
}
.text-disabled.data-v-ab57d952 {
  color: #BFBFBF;
}
.text-success.data-v-ab57d952 {
  color: #52C41A;
}
.text-warning.data-v-ab57d952 {
  color: #FAAD14;
}
.text-error.data-v-ab57d952 {
  color: #F5222D;
}
.text-primary-color.data-v-ab57d952 {
  color: #2E8B57;
}
.text-center.data-v-ab57d952 {
  text-align: center;
}
.text-left.data-v-ab57d952 {
  text-align: left;
}
.text-right.data-v-ab57d952 {
  text-align: right;
}
.text-bold.data-v-ab57d952 {
  font-weight: bold;
}
.text-normal.data-v-ab57d952 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-ab57d952 {
  font-size: 20rpx;
}
.text-sm.data-v-ab57d952 {
  font-size: 24rpx;
}
.text-base.data-v-ab57d952 {
  font-size: 28rpx;
}
.text-lg.data-v-ab57d952 {
  font-size: 32rpx;
}
.text-xl.data-v-ab57d952 {
  font-size: 36rpx;
}
.text-2xl.data-v-ab57d952 {
  font-size: 40rpx;
}
.text-3xl.data-v-ab57d952 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-ab57d952 {
  margin: 0;
}
.m-1.data-v-ab57d952 {
  margin: 8rpx;
}
.m-2.data-v-ab57d952 {
  margin: 16rpx;
}
.m-3.data-v-ab57d952 {
  margin: 24rpx;
}
.m-4.data-v-ab57d952 {
  margin: 32rpx;
}
.m-5.data-v-ab57d952 {
  margin: 40rpx;
}
.mt-0.data-v-ab57d952 {
  margin-top: 0;
}
.mt-1.data-v-ab57d952 {
  margin-top: 8rpx;
}
.mt-2.data-v-ab57d952 {
  margin-top: 16rpx;
}
.mt-3.data-v-ab57d952 {
  margin-top: 24rpx;
}
.mt-4.data-v-ab57d952 {
  margin-top: 32rpx;
}
.mt-5.data-v-ab57d952 {
  margin-top: 40rpx;
}
.mb-0.data-v-ab57d952 {
  margin-bottom: 0;
}
.mb-1.data-v-ab57d952 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-ab57d952 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-ab57d952 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-ab57d952 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-ab57d952 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-ab57d952 {
  margin-left: 0;
}
.ml-1.data-v-ab57d952 {
  margin-left: 8rpx;
}
.ml-2.data-v-ab57d952 {
  margin-left: 16rpx;
}
.ml-3.data-v-ab57d952 {
  margin-left: 24rpx;
}
.ml-4.data-v-ab57d952 {
  margin-left: 32rpx;
}
.ml-5.data-v-ab57d952 {
  margin-left: 40rpx;
}
.mr-0.data-v-ab57d952 {
  margin-right: 0;
}
.mr-1.data-v-ab57d952 {
  margin-right: 8rpx;
}
.mr-2.data-v-ab57d952 {
  margin-right: 16rpx;
}
.mr-3.data-v-ab57d952 {
  margin-right: 24rpx;
}
.mr-4.data-v-ab57d952 {
  margin-right: 32rpx;
}
.mr-5.data-v-ab57d952 {
  margin-right: 40rpx;
}
.p-0.data-v-ab57d952 {
  padding: 0;
}
.p-1.data-v-ab57d952 {
  padding: 8rpx;
}
.p-2.data-v-ab57d952 {
  padding: 16rpx;
}
.p-3.data-v-ab57d952 {
  padding: 24rpx;
}
.p-4.data-v-ab57d952 {
  padding: 32rpx;
}
.p-5.data-v-ab57d952 {
  padding: 40rpx;
}
.pt-0.data-v-ab57d952 {
  padding-top: 0;
}
.pt-1.data-v-ab57d952 {
  padding-top: 8rpx;
}
.pt-2.data-v-ab57d952 {
  padding-top: 16rpx;
}
.pt-3.data-v-ab57d952 {
  padding-top: 24rpx;
}
.pt-4.data-v-ab57d952 {
  padding-top: 32rpx;
}
.pt-5.data-v-ab57d952 {
  padding-top: 40rpx;
}
.pb-0.data-v-ab57d952 {
  padding-bottom: 0;
}
.pb-1.data-v-ab57d952 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-ab57d952 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-ab57d952 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-ab57d952 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-ab57d952 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-ab57d952 {
  padding-left: 0;
}
.pl-1.data-v-ab57d952 {
  padding-left: 8rpx;
}
.pl-2.data-v-ab57d952 {
  padding-left: 16rpx;
}
.pl-3.data-v-ab57d952 {
  padding-left: 24rpx;
}
.pl-4.data-v-ab57d952 {
  padding-left: 32rpx;
}
.pl-5.data-v-ab57d952 {
  padding-left: 40rpx;
}
.pr-0.data-v-ab57d952 {
  padding-right: 0;
}
.pr-1.data-v-ab57d952 {
  padding-right: 8rpx;
}
.pr-2.data-v-ab57d952 {
  padding-right: 16rpx;
}
.pr-3.data-v-ab57d952 {
  padding-right: 24rpx;
}
.pr-4.data-v-ab57d952 {
  padding-right: 32rpx;
}
.pr-5.data-v-ab57d952 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-ab57d952 {
  width: 100%;
}
.h-full.data-v-ab57d952 {
  height: 100%;
}
.w-screen.data-v-ab57d952 {
  width: 100vw;
}
.h-screen.data-v-ab57d952 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-ab57d952 {
  border-radius: 0;
}
.rounded-sm.data-v-ab57d952 {
  border-radius: 4rpx;
}
.rounded.data-v-ab57d952 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-ab57d952 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-ab57d952 {
  border-radius: 24rpx;
}
.rounded-full.data-v-ab57d952 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-ab57d952 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-ab57d952 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-ab57d952 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-ab57d952 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-ab57d952 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-ab57d952 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-ab57d952 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-ab57d952 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-ab57d952 {
  background-color: #2E8B57;
}
.bg-light.data-v-ab57d952 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-ab57d952 {
  background-color: #F8F9FA;
}
.bg-success.data-v-ab57d952 {
  background-color: #52C41A;
}
.bg-warning.data-v-ab57d952 {
  background-color: #FAAD14;
}
.bg-error.data-v-ab57d952 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-ab57d952 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-ab57d952 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-ab57d952 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-ab57d952 {
  color: #FAAD14;
}
.status-approved.data-v-ab57d952 {
  color: #52C41A;
}
.status-rejected.data-v-ab57d952 {
  color: #F5222D;
}
.status-not-submitted.data-v-ab57d952 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-ab57d952 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-ab57d952 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-ab57d952 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-ab57d952 {
  animation: fadeIn-ab57d952 0.3s ease-in-out;
}
@keyframes fadeIn-ab57d952 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-ab57d952 {
  animation: slideUp-ab57d952 0.3s ease-out;
}
@keyframes slideUp-ab57d952 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.detail-container.data-v-ab57d952 {
  min-height: 100vh;
  background: #F8F9FA;
}
.detail-content.data-v-ab57d952 {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.article-header.data-v-ab57d952 {
  padding: 40rpx;
}
.article-header .header-tags.data-v-ab57d952 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.article-header .header-tags .important-tag.data-v-ab57d952,
.article-header .header-tags .top-tag.data-v-ab57d952 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.article-header .header-tags .important-tag text.data-v-ab57d952,
.article-header .header-tags .top-tag text.data-v-ab57d952 {
  font-weight: bold;
}
.article-header .header-tags .important-tag.data-v-ab57d952 {
  background: #fff7e6;
  color: #FAAD14;
}
.article-header .header-tags .top-tag.data-v-ab57d952 {
  background: #f6ffed;
  color: #52C41A;
}
.article-header .article-title.data-v-ab57d952 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 32rpx;
}
.article-header .article-meta.data-v-ab57d952 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.article-header .article-meta .meta-item.data-v-ab57d952 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.article-header .article-meta .meta-item text.data-v-ab57d952 {
  font-size: 26rpx;
  color: #595959;
}
.divider.data-v-ab57d952 {
  height: 2rpx;
  background: #F0F0F0;
  margin: 0 40rpx;
}
.article-body.data-v-ab57d952 {
  padding: 40rpx;
}
.article-body .rich-content.data-v-ab57d952 {
  font-size: 30rpx;
  line-height: 1.8;
  color: #262626;
}
.article-body .rich-content.data-v-ab57d952 p {
  margin-bottom: 24rpx;
  text-align: justify;
}
.article-body .rich-content.data-v-ab57d952 img {
  max-width: 100%;
  height: auto;
  border-radius: 8rpx;
  margin: 16rpx 0;
}
.article-body .rich-content.data-v-ab57d952 strong {
  font-weight: bold;
  color: #2E8B57;
}
.article-actions.data-v-ab57d952 {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 40rpx;
  border-top: 2rpx solid #F0F0F0;
}
.article-actions .action-item.data-v-ab57d952 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
}
.article-actions .action-item text.data-v-ab57d952 {
  font-size: 24rpx;
  color: #595959;
}
.related-section.data-v-ab57d952 {
  margin: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.related-section .section-title.data-v-ab57d952 {
  padding: 32rpx 40rpx 24rpx;
  border-bottom: 2rpx solid #F0F0F0;
}
.related-section .section-title text.data-v-ab57d952 {
  font-size: 32rpx;
  font-weight: bold;
  color: #262626;
}
.related-section .related-list.data-v-ab57d952 {
  padding: 24rpx 40rpx 40rpx;
}
.related-section .related-list .related-item.data-v-ab57d952 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}
.related-section .related-list .related-item.data-v-ab57d952:last-child {
  border-bottom: none;
}
.related-section .related-list .related-item .related-title.data-v-ab57d952 {
  flex: 1;
  font-size: 28rpx;
  color: #262626;
  line-height: 1.4;
  margin-right: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.related-section .related-list .related-item .related-date.data-v-ab57d952 {
  font-size: 24rpx;
  color: #BFBFBF;
  white-space: nowrap;
}