/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-f52d2d81 {
  box-sizing: border-box;
}
page.data-v-f52d2d81 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-f52d2d81 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-f52d2d81 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-f52d2d81 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-f52d2d81 {
  display: flex;
}
.flex-column.data-v-f52d2d81 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-f52d2d81 {
  flex: 1;
}
.flex-wrap.data-v-f52d2d81 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-f52d2d81 {
  color: #262626;
}
.text-secondary.data-v-f52d2d81 {
  color: #595959;
}
.text-disabled.data-v-f52d2d81 {
  color: #BFBFBF;
}
.text-success.data-v-f52d2d81 {
  color: #52C41A;
}
.text-warning.data-v-f52d2d81 {
  color: #FAAD14;
}
.text-error.data-v-f52d2d81 {
  color: #F5222D;
}
.text-primary-color.data-v-f52d2d81 {
  color: #2E8B57;
}
.text-center.data-v-f52d2d81 {
  text-align: center;
}
.text-left.data-v-f52d2d81 {
  text-align: left;
}
.text-right.data-v-f52d2d81 {
  text-align: right;
}
.text-bold.data-v-f52d2d81 {
  font-weight: bold;
}
.text-normal.data-v-f52d2d81 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-f52d2d81 {
  font-size: 20rpx;
}
.text-sm.data-v-f52d2d81 {
  font-size: 24rpx;
}
.text-base.data-v-f52d2d81 {
  font-size: 28rpx;
}
.text-lg.data-v-f52d2d81 {
  font-size: 32rpx;
}
.text-xl.data-v-f52d2d81 {
  font-size: 36rpx;
}
.text-2xl.data-v-f52d2d81 {
  font-size: 40rpx;
}
.text-3xl.data-v-f52d2d81 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-f52d2d81 {
  margin: 0;
}
.m-1.data-v-f52d2d81 {
  margin: 8rpx;
}
.m-2.data-v-f52d2d81 {
  margin: 16rpx;
}
.m-3.data-v-f52d2d81 {
  margin: 24rpx;
}
.m-4.data-v-f52d2d81 {
  margin: 32rpx;
}
.m-5.data-v-f52d2d81 {
  margin: 40rpx;
}
.mt-0.data-v-f52d2d81 {
  margin-top: 0;
}
.mt-1.data-v-f52d2d81 {
  margin-top: 8rpx;
}
.mt-2.data-v-f52d2d81 {
  margin-top: 16rpx;
}
.mt-3.data-v-f52d2d81 {
  margin-top: 24rpx;
}
.mt-4.data-v-f52d2d81 {
  margin-top: 32rpx;
}
.mt-5.data-v-f52d2d81 {
  margin-top: 40rpx;
}
.mb-0.data-v-f52d2d81 {
  margin-bottom: 0;
}
.mb-1.data-v-f52d2d81 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-f52d2d81 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-f52d2d81 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-f52d2d81 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-f52d2d81 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-f52d2d81 {
  margin-left: 0;
}
.ml-1.data-v-f52d2d81 {
  margin-left: 8rpx;
}
.ml-2.data-v-f52d2d81 {
  margin-left: 16rpx;
}
.ml-3.data-v-f52d2d81 {
  margin-left: 24rpx;
}
.ml-4.data-v-f52d2d81 {
  margin-left: 32rpx;
}
.ml-5.data-v-f52d2d81 {
  margin-left: 40rpx;
}
.mr-0.data-v-f52d2d81 {
  margin-right: 0;
}
.mr-1.data-v-f52d2d81 {
  margin-right: 8rpx;
}
.mr-2.data-v-f52d2d81 {
  margin-right: 16rpx;
}
.mr-3.data-v-f52d2d81 {
  margin-right: 24rpx;
}
.mr-4.data-v-f52d2d81 {
  margin-right: 32rpx;
}
.mr-5.data-v-f52d2d81 {
  margin-right: 40rpx;
}
.p-0.data-v-f52d2d81 {
  padding: 0;
}
.p-1.data-v-f52d2d81 {
  padding: 8rpx;
}
.p-2.data-v-f52d2d81 {
  padding: 16rpx;
}
.p-3.data-v-f52d2d81 {
  padding: 24rpx;
}
.p-4.data-v-f52d2d81 {
  padding: 32rpx;
}
.p-5.data-v-f52d2d81 {
  padding: 40rpx;
}
.pt-0.data-v-f52d2d81 {
  padding-top: 0;
}
.pt-1.data-v-f52d2d81 {
  padding-top: 8rpx;
}
.pt-2.data-v-f52d2d81 {
  padding-top: 16rpx;
}
.pt-3.data-v-f52d2d81 {
  padding-top: 24rpx;
}
.pt-4.data-v-f52d2d81 {
  padding-top: 32rpx;
}
.pt-5.data-v-f52d2d81 {
  padding-top: 40rpx;
}
.pb-0.data-v-f52d2d81 {
  padding-bottom: 0;
}
.pb-1.data-v-f52d2d81 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-f52d2d81 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-f52d2d81 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-f52d2d81 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-f52d2d81 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-f52d2d81 {
  padding-left: 0;
}
.pl-1.data-v-f52d2d81 {
  padding-left: 8rpx;
}
.pl-2.data-v-f52d2d81 {
  padding-left: 16rpx;
}
.pl-3.data-v-f52d2d81 {
  padding-left: 24rpx;
}
.pl-4.data-v-f52d2d81 {
  padding-left: 32rpx;
}
.pl-5.data-v-f52d2d81 {
  padding-left: 40rpx;
}
.pr-0.data-v-f52d2d81 {
  padding-right: 0;
}
.pr-1.data-v-f52d2d81 {
  padding-right: 8rpx;
}
.pr-2.data-v-f52d2d81 {
  padding-right: 16rpx;
}
.pr-3.data-v-f52d2d81 {
  padding-right: 24rpx;
}
.pr-4.data-v-f52d2d81 {
  padding-right: 32rpx;
}
.pr-5.data-v-f52d2d81 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-f52d2d81 {
  width: 100%;
}
.h-full.data-v-f52d2d81 {
  height: 100%;
}
.w-screen.data-v-f52d2d81 {
  width: 100vw;
}
.h-screen.data-v-f52d2d81 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-f52d2d81 {
  border-radius: 0;
}
.rounded-sm.data-v-f52d2d81 {
  border-radius: 4rpx;
}
.rounded.data-v-f52d2d81 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-f52d2d81 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-f52d2d81 {
  border-radius: 24rpx;
}
.rounded-full.data-v-f52d2d81 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-f52d2d81 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-f52d2d81 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-f52d2d81 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-f52d2d81 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-f52d2d81 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-f52d2d81 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-f52d2d81 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-f52d2d81 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-f52d2d81 {
  background-color: #2E8B57;
}
.bg-light.data-v-f52d2d81 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-f52d2d81 {
  background-color: #F8F9FA;
}
.bg-success.data-v-f52d2d81 {
  background-color: #52C41A;
}
.bg-warning.data-v-f52d2d81 {
  background-color: #FAAD14;
}
.bg-error.data-v-f52d2d81 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-f52d2d81 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-f52d2d81 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-f52d2d81 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-f52d2d81 {
  color: #FAAD14;
}
.status-approved.data-v-f52d2d81 {
  color: #52C41A;
}
.status-rejected.data-v-f52d2d81 {
  color: #F5222D;
}
.status-not-submitted.data-v-f52d2d81 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-f52d2d81 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-f52d2d81 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-f52d2d81 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-f52d2d81 {
  animation: fadeIn-f52d2d81 0.3s ease-in-out;
}
@keyframes fadeIn-f52d2d81 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-f52d2d81 {
  animation: slideUp-f52d2d81 0.3s ease-out;
}
@keyframes slideUp-f52d2d81 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.info-center-container.data-v-f52d2d81 {
  min-height: 100vh;
  background: #F8F9FA;
}
.status-bar.data-v-f52d2d81 {
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
}
.header.data-v-f52d2d81 {
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  padding: 20rpx 30rpx 40rpx;
}
.header .header-content.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header .header-content .page-title.data-v-f52d2d81 {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}
.header .header-content .header-actions.data-v-f52d2d81 {
  display: flex;
  align-items: center;
}
.main-content.data-v-f52d2d81 {
  padding-bottom: 120rpx;
}
.banner-section.data-v-f52d2d81 {
  margin: 30rpx;
}
.banner-section .banner-swiper.data-v-f52d2d81 {
  height: 200rpx;
  border-radius: 24rpx;
  overflow: hidden;
}
.banner-section .banner-swiper .banner-item.data-v-f52d2d81 {
  position: relative;
  height: 100%;
  background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);
  padding: 40rpx;
  display: flex;
  align-items: center;
}
.banner-section .banner-swiper .banner-item .banner-content.data-v-f52d2d81 {
  flex: 1;
}
.banner-section .banner-swiper .banner-item .banner-content .banner-title.data-v-f52d2d81 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  line-height: 1.4;
}
.banner-section .banner-swiper .banner-item .banner-content .banner-desc.data-v-f52d2d81 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}
.banner-section .banner-swiper .banner-item .banner-tag.data-v-f52d2d81 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 149, 0, 0.9);
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
}
.banner-section .banner-swiper .banner-item .banner-tag.important.data-v-f52d2d81 {
  background: rgba(245, 34, 45, 0.9);
}
.content-categories.data-v-f52d2d81 {
  background: #fff;
  padding: 0 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.content-list .list-scroll.data-v-f52d2d81 {
  height: calc(100vh - 400rpx);
}
.content-list .content-item.data-v-f52d2d81 {
  margin: 20rpx 30rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}
.content-list .content-item .item-header.data-v-f52d2d81 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
  gap: 16rpx;
}
.content-list .content-item .item-header .item-title.data-v-f52d2d81 {
  flex: 1;
  font-size: 30rpx;
  font-weight: bold;
  color: #262626;
  line-height: 1.4;
}
.content-list .content-item .item-header .important-tag.data-v-f52d2d81 {
  padding: 8rpx 16rpx;
  background: #fff3e0;
  border-radius: 12rpx;
  border: 2rpx solid #FAAD14;
}
.content-list .content-item .item-header .important-tag text.data-v-f52d2d81 {
  font-size: 20rpx;
  color: #FAAD14;
  font-weight: bold;
}
.content-list .content-item .item-header .top-tag.data-v-f52d2d81 {
  padding: 8rpx 16rpx;
  background: #f6ffed;
  border-radius: 12rpx;
  border: 2rpx solid #52C41A;
}
.content-list .content-item .item-header .top-tag text.data-v-f52d2d81 {
  font-size: 20rpx;
  color: #52C41A;
  font-weight: bold;
}
.content-list .content-item .item-summary.data-v-f52d2d81 {
  display: block;
  font-size: 26rpx;
  color: #595959;
  line-height: 1.5;
  margin-bottom: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.content-list .content-item .item-footer.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content-list .content-item .item-footer .item-date.data-v-f52d2d81 {
  font-size: 24rpx;
  color: #BFBFBF;
}
.content-list .content-item .item-footer .item-meta.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.content-list .content-item .item-footer .item-meta .item-source.data-v-f52d2d81 {
  font-size: 24rpx;
  color: #BFBFBF;
}
.loading-more.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}
.no-more.data-v-f52d2d81 {
  text-align: center;
  padding: 40rpx;
  color: #BFBFBF;
  font-size: 26rpx;
}