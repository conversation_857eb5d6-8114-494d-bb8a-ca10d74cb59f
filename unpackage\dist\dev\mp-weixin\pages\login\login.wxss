/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-e4e4508d {
  box-sizing: border-box;
}
page.data-v-e4e4508d {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-e4e4508d {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-e4e4508d {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-e4e4508d {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-e4e4508d {
  display: flex;
}
.flex-column.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-e4e4508d {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-e4e4508d {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-e4e4508d {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-e4e4508d {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-e4e4508d {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-e4e4508d {
  flex: 1;
}
.flex-wrap.data-v-e4e4508d {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-e4e4508d {
  color: #262626;
}
.text-secondary.data-v-e4e4508d {
  color: #595959;
}
.text-disabled.data-v-e4e4508d {
  color: #BFBFBF;
}
.text-success.data-v-e4e4508d {
  color: #52C41A;
}
.text-warning.data-v-e4e4508d {
  color: #FAAD14;
}
.text-error.data-v-e4e4508d {
  color: #F5222D;
}
.text-primary-color.data-v-e4e4508d {
  color: #2E8B57;
}
.text-center.data-v-e4e4508d {
  text-align: center;
}
.text-left.data-v-e4e4508d {
  text-align: left;
}
.text-right.data-v-e4e4508d {
  text-align: right;
}
.text-bold.data-v-e4e4508d {
  font-weight: bold;
}
.text-normal.data-v-e4e4508d {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-e4e4508d {
  font-size: 20rpx;
}
.text-sm.data-v-e4e4508d {
  font-size: 24rpx;
}
.text-base.data-v-e4e4508d {
  font-size: 28rpx;
}
.text-lg.data-v-e4e4508d {
  font-size: 32rpx;
}
.text-xl.data-v-e4e4508d {
  font-size: 36rpx;
}
.text-2xl.data-v-e4e4508d {
  font-size: 40rpx;
}
.text-3xl.data-v-e4e4508d {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-e4e4508d {
  margin: 0;
}
.m-1.data-v-e4e4508d {
  margin: 8rpx;
}
.m-2.data-v-e4e4508d {
  margin: 16rpx;
}
.m-3.data-v-e4e4508d {
  margin: 24rpx;
}
.m-4.data-v-e4e4508d {
  margin: 32rpx;
}
.m-5.data-v-e4e4508d {
  margin: 40rpx;
}
.mt-0.data-v-e4e4508d {
  margin-top: 0;
}
.mt-1.data-v-e4e4508d {
  margin-top: 8rpx;
}
.mt-2.data-v-e4e4508d {
  margin-top: 16rpx;
}
.mt-3.data-v-e4e4508d {
  margin-top: 24rpx;
}
.mt-4.data-v-e4e4508d {
  margin-top: 32rpx;
}
.mt-5.data-v-e4e4508d {
  margin-top: 40rpx;
}
.mb-0.data-v-e4e4508d {
  margin-bottom: 0;
}
.mb-1.data-v-e4e4508d {
  margin-bottom: 8rpx;
}
.mb-2.data-v-e4e4508d {
  margin-bottom: 16rpx;
}
.mb-3.data-v-e4e4508d {
  margin-bottom: 24rpx;
}
.mb-4.data-v-e4e4508d {
  margin-bottom: 32rpx;
}
.mb-5.data-v-e4e4508d {
  margin-bottom: 40rpx;
}
.ml-0.data-v-e4e4508d {
  margin-left: 0;
}
.ml-1.data-v-e4e4508d {
  margin-left: 8rpx;
}
.ml-2.data-v-e4e4508d {
  margin-left: 16rpx;
}
.ml-3.data-v-e4e4508d {
  margin-left: 24rpx;
}
.ml-4.data-v-e4e4508d {
  margin-left: 32rpx;
}
.ml-5.data-v-e4e4508d {
  margin-left: 40rpx;
}
.mr-0.data-v-e4e4508d {
  margin-right: 0;
}
.mr-1.data-v-e4e4508d {
  margin-right: 8rpx;
}
.mr-2.data-v-e4e4508d {
  margin-right: 16rpx;
}
.mr-3.data-v-e4e4508d {
  margin-right: 24rpx;
}
.mr-4.data-v-e4e4508d {
  margin-right: 32rpx;
}
.mr-5.data-v-e4e4508d {
  margin-right: 40rpx;
}
.p-0.data-v-e4e4508d {
  padding: 0;
}
.p-1.data-v-e4e4508d {
  padding: 8rpx;
}
.p-2.data-v-e4e4508d {
  padding: 16rpx;
}
.p-3.data-v-e4e4508d {
  padding: 24rpx;
}
.p-4.data-v-e4e4508d {
  padding: 32rpx;
}
.p-5.data-v-e4e4508d {
  padding: 40rpx;
}
.pt-0.data-v-e4e4508d {
  padding-top: 0;
}
.pt-1.data-v-e4e4508d {
  padding-top: 8rpx;
}
.pt-2.data-v-e4e4508d {
  padding-top: 16rpx;
}
.pt-3.data-v-e4e4508d {
  padding-top: 24rpx;
}
.pt-4.data-v-e4e4508d {
  padding-top: 32rpx;
}
.pt-5.data-v-e4e4508d {
  padding-top: 40rpx;
}
.pb-0.data-v-e4e4508d {
  padding-bottom: 0;
}
.pb-1.data-v-e4e4508d {
  padding-bottom: 8rpx;
}
.pb-2.data-v-e4e4508d {
  padding-bottom: 16rpx;
}
.pb-3.data-v-e4e4508d {
  padding-bottom: 24rpx;
}
.pb-4.data-v-e4e4508d {
  padding-bottom: 32rpx;
}
.pb-5.data-v-e4e4508d {
  padding-bottom: 40rpx;
}
.pl-0.data-v-e4e4508d {
  padding-left: 0;
}
.pl-1.data-v-e4e4508d {
  padding-left: 8rpx;
}
.pl-2.data-v-e4e4508d {
  padding-left: 16rpx;
}
.pl-3.data-v-e4e4508d {
  padding-left: 24rpx;
}
.pl-4.data-v-e4e4508d {
  padding-left: 32rpx;
}
.pl-5.data-v-e4e4508d {
  padding-left: 40rpx;
}
.pr-0.data-v-e4e4508d {
  padding-right: 0;
}
.pr-1.data-v-e4e4508d {
  padding-right: 8rpx;
}
.pr-2.data-v-e4e4508d {
  padding-right: 16rpx;
}
.pr-3.data-v-e4e4508d {
  padding-right: 24rpx;
}
.pr-4.data-v-e4e4508d {
  padding-right: 32rpx;
}
.pr-5.data-v-e4e4508d {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-e4e4508d {
  width: 100%;
}
.h-full.data-v-e4e4508d {
  height: 100%;
}
.w-screen.data-v-e4e4508d {
  width: 100vw;
}
.h-screen.data-v-e4e4508d {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-e4e4508d {
  border-radius: 0;
}
.rounded-sm.data-v-e4e4508d {
  border-radius: 4rpx;
}
.rounded.data-v-e4e4508d {
  border-radius: 8rpx;
}
.rounded-lg.data-v-e4e4508d {
  border-radius: 16rpx;
}
.rounded-xl.data-v-e4e4508d {
  border-radius: 24rpx;
}
.rounded-full.data-v-e4e4508d {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-e4e4508d {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-e4e4508d {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-e4e4508d {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-e4e4508d {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-e4e4508d {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-e4e4508d {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-e4e4508d {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-e4e4508d {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-e4e4508d {
  background-color: #2E8B57;
}
.bg-light.data-v-e4e4508d {
  background-color: #FFFFFF;
}
.bg-gray.data-v-e4e4508d {
  background-color: #F8F9FA;
}
.bg-success.data-v-e4e4508d {
  background-color: #52C41A;
}
.bg-warning.data-v-e4e4508d {
  background-color: #FAAD14;
}
.bg-error.data-v-e4e4508d {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-e4e4508d {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-e4e4508d {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-e4e4508d {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-e4e4508d {
  color: #FAAD14;
}
.status-approved.data-v-e4e4508d {
  color: #52C41A;
}
.status-rejected.data-v-e4e4508d {
  color: #F5222D;
}
.status-not-submitted.data-v-e4e4508d {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-e4e4508d {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-e4e4508d {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-e4e4508d {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-e4e4508d {
  animation: fadeIn-e4e4508d 0.3s ease-in-out;
}
@keyframes fadeIn-e4e4508d {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-e4e4508d {
  animation: slideUp-e4e4508d 0.3s ease-out;
}
@keyframes slideUp-e4e4508d {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.login-container.data-v-e4e4508d {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx 60rpx;
}
.bg-decoration.data-v-e4e4508d {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.bg-decoration .circle.data-v-e4e4508d {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}
.bg-decoration .circle.circle-1.data-v-e4e4508d {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation: float-e4e4508d 6s ease-in-out infinite;
}
.bg-decoration .circle.circle-2.data-v-e4e4508d {
  width: 150rpx;
  height: 150rpx;
  bottom: 20%;
  left: 15%;
  animation: float-e4e4508d 8s ease-in-out infinite reverse;
}
.bg-decoration .circle.circle-3.data-v-e4e4508d {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 10%;
  animation: float-e4e4508d 7s ease-in-out infinite;
}
@keyframes float-e4e4508d {
0%, 100% {
    transform: translateY(0px);
}
50% {
    transform: translateY(-20rpx);
}
}
.main-content.data-v-e4e4508d {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-width: 500rpx;
}
.header.data-v-e4e4508d {
  text-align: center;
  margin-bottom: 100rpx;
}
.header .logo.data-v-e4e4508d {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.header .app-name.data-v-e4e4508d {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.header .app-desc.data-v-e4e4508d {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}
.login-form.data-v-e4e4508d {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}
.agreement-section.data-v-e4e4508d {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
}
.agreement-section .agreement-text.data-v-e4e4508d {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
.agreement-section .agreement-text .link-text.data-v-e4e4508d {
  color: #2E8B57;
  text-decoration: underline;
}
.login-btn.data-v-e4e4508d {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  box-shadow: 0 8rpx 24rpx rgba(46, 139, 87, 0.3);
  margin-bottom: 40rpx;
}
.login-btn .wechat-icon.data-v-e4e4508d {
  margin-right: 16rpx;
}
.tips.data-v-e4e4508d {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 6rpx solid #2E8B57;
}
.tips .tips-text.data-v-e4e4508d {
  flex: 1;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-left: 12rpx;
}
.footer.data-v-e4e4508d {
  margin-top: 60rpx;
}
.footer .copyright.data-v-e4e4508d {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}
.agreement-modal.data-v-e4e4508d {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}
.agreement-modal .modal-header.data-v-e4e4508d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.agreement-modal .modal-header .modal-title.data-v-e4e4508d {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.agreement-modal .modal-content.data-v-e4e4508d {
  flex: 1;
  padding: 40rpx;
}
.agreement-modal .modal-content .agreement-content.data-v-e4e4508d {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}
.agreement-modal .modal-footer.data-v-e4e4508d {
  padding: 40rpx;
  border-top: 2rpx solid #f0f0f0;
}