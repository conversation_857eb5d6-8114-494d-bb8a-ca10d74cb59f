/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-6ae23533 {
  box-sizing: border-box;
}
page.data-v-6ae23533 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-6ae23533 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-6ae23533 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-6ae23533 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-6ae23533 {
  display: flex;
}
.flex-column.data-v-6ae23533 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-6ae23533 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-6ae23533 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-6ae23533 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-6ae23533 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-6ae23533 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-6ae23533 {
  flex: 1;
}
.flex-wrap.data-v-6ae23533 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-6ae23533 {
  color: #262626;
}
.text-secondary.data-v-6ae23533 {
  color: #595959;
}
.text-disabled.data-v-6ae23533 {
  color: #BFBFBF;
}
.text-success.data-v-6ae23533 {
  color: #52C41A;
}
.text-warning.data-v-6ae23533 {
  color: #FAAD14;
}
.text-error.data-v-6ae23533 {
  color: #F5222D;
}
.text-primary-color.data-v-6ae23533 {
  color: #2E8B57;
}
.text-center.data-v-6ae23533 {
  text-align: center;
}
.text-left.data-v-6ae23533 {
  text-align: left;
}
.text-right.data-v-6ae23533 {
  text-align: right;
}
.text-bold.data-v-6ae23533 {
  font-weight: bold;
}
.text-normal.data-v-6ae23533 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-6ae23533 {
  font-size: 20rpx;
}
.text-sm.data-v-6ae23533 {
  font-size: 24rpx;
}
.text-base.data-v-6ae23533 {
  font-size: 28rpx;
}
.text-lg.data-v-6ae23533 {
  font-size: 32rpx;
}
.text-xl.data-v-6ae23533 {
  font-size: 36rpx;
}
.text-2xl.data-v-6ae23533 {
  font-size: 40rpx;
}
.text-3xl.data-v-6ae23533 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-6ae23533 {
  margin: 0;
}
.m-1.data-v-6ae23533 {
  margin: 8rpx;
}
.m-2.data-v-6ae23533 {
  margin: 16rpx;
}
.m-3.data-v-6ae23533 {
  margin: 24rpx;
}
.m-4.data-v-6ae23533 {
  margin: 32rpx;
}
.m-5.data-v-6ae23533 {
  margin: 40rpx;
}
.mt-0.data-v-6ae23533 {
  margin-top: 0;
}
.mt-1.data-v-6ae23533 {
  margin-top: 8rpx;
}
.mt-2.data-v-6ae23533 {
  margin-top: 16rpx;
}
.mt-3.data-v-6ae23533 {
  margin-top: 24rpx;
}
.mt-4.data-v-6ae23533 {
  margin-top: 32rpx;
}
.mt-5.data-v-6ae23533 {
  margin-top: 40rpx;
}
.mb-0.data-v-6ae23533 {
  margin-bottom: 0;
}
.mb-1.data-v-6ae23533 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-6ae23533 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-6ae23533 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-6ae23533 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-6ae23533 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-6ae23533 {
  margin-left: 0;
}
.ml-1.data-v-6ae23533 {
  margin-left: 8rpx;
}
.ml-2.data-v-6ae23533 {
  margin-left: 16rpx;
}
.ml-3.data-v-6ae23533 {
  margin-left: 24rpx;
}
.ml-4.data-v-6ae23533 {
  margin-left: 32rpx;
}
.ml-5.data-v-6ae23533 {
  margin-left: 40rpx;
}
.mr-0.data-v-6ae23533 {
  margin-right: 0;
}
.mr-1.data-v-6ae23533 {
  margin-right: 8rpx;
}
.mr-2.data-v-6ae23533 {
  margin-right: 16rpx;
}
.mr-3.data-v-6ae23533 {
  margin-right: 24rpx;
}
.mr-4.data-v-6ae23533 {
  margin-right: 32rpx;
}
.mr-5.data-v-6ae23533 {
  margin-right: 40rpx;
}
.p-0.data-v-6ae23533 {
  padding: 0;
}
.p-1.data-v-6ae23533 {
  padding: 8rpx;
}
.p-2.data-v-6ae23533 {
  padding: 16rpx;
}
.p-3.data-v-6ae23533 {
  padding: 24rpx;
}
.p-4.data-v-6ae23533 {
  padding: 32rpx;
}
.p-5.data-v-6ae23533 {
  padding: 40rpx;
}
.pt-0.data-v-6ae23533 {
  padding-top: 0;
}
.pt-1.data-v-6ae23533 {
  padding-top: 8rpx;
}
.pt-2.data-v-6ae23533 {
  padding-top: 16rpx;
}
.pt-3.data-v-6ae23533 {
  padding-top: 24rpx;
}
.pt-4.data-v-6ae23533 {
  padding-top: 32rpx;
}
.pt-5.data-v-6ae23533 {
  padding-top: 40rpx;
}
.pb-0.data-v-6ae23533 {
  padding-bottom: 0;
}
.pb-1.data-v-6ae23533 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-6ae23533 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-6ae23533 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-6ae23533 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-6ae23533 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-6ae23533 {
  padding-left: 0;
}
.pl-1.data-v-6ae23533 {
  padding-left: 8rpx;
}
.pl-2.data-v-6ae23533 {
  padding-left: 16rpx;
}
.pl-3.data-v-6ae23533 {
  padding-left: 24rpx;
}
.pl-4.data-v-6ae23533 {
  padding-left: 32rpx;
}
.pl-5.data-v-6ae23533 {
  padding-left: 40rpx;
}
.pr-0.data-v-6ae23533 {
  padding-right: 0;
}
.pr-1.data-v-6ae23533 {
  padding-right: 8rpx;
}
.pr-2.data-v-6ae23533 {
  padding-right: 16rpx;
}
.pr-3.data-v-6ae23533 {
  padding-right: 24rpx;
}
.pr-4.data-v-6ae23533 {
  padding-right: 32rpx;
}
.pr-5.data-v-6ae23533 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-6ae23533 {
  width: 100%;
}
.h-full.data-v-6ae23533 {
  height: 100%;
}
.w-screen.data-v-6ae23533 {
  width: 100vw;
}
.h-screen.data-v-6ae23533 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-6ae23533 {
  border-radius: 0;
}
.rounded-sm.data-v-6ae23533 {
  border-radius: 4rpx;
}
.rounded.data-v-6ae23533 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-6ae23533 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-6ae23533 {
  border-radius: 24rpx;
}
.rounded-full.data-v-6ae23533 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-6ae23533 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-6ae23533 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-6ae23533 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-6ae23533 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-6ae23533 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-6ae23533 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-6ae23533 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-6ae23533 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-6ae23533 {
  background-color: #2E8B57;
}
.bg-light.data-v-6ae23533 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-6ae23533 {
  background-color: #F8F9FA;
}
.bg-success.data-v-6ae23533 {
  background-color: #52C41A;
}
.bg-warning.data-v-6ae23533 {
  background-color: #FAAD14;
}
.bg-error.data-v-6ae23533 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-6ae23533 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-6ae23533 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-6ae23533 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-6ae23533 {
  color: #FAAD14;
}
.status-approved.data-v-6ae23533 {
  color: #52C41A;
}
.status-rejected.data-v-6ae23533 {
  color: #F5222D;
}
.status-not-submitted.data-v-6ae23533 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-6ae23533 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-6ae23533 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-6ae23533 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-6ae23533 {
  animation: fadeIn-6ae23533 0.3s ease-in-out;
}
@keyframes fadeIn-6ae23533 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-6ae23533 {
  animation: slideUp-6ae23533 0.3s ease-out;
}
@keyframes slideUp-6ae23533 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.personal-container.data-v-6ae23533 {
  min-height: 100vh;
  background: #F8F9FA;
}
.status-bar.data-v-6ae23533 {
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
}
.user-header.data-v-6ae23533 {
  position: relative;
  padding-bottom: 40rpx;
}
.user-header .header-bg.data-v-6ae23533 {
  height: 200rpx;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
}
.user-header .user-info.data-v-6ae23533 {
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}
.user-header .user-info .avatar-section.data-v-6ae23533 {
  position: relative;
  margin-right: 32rpx;
}
.user-header .user-info .avatar-section .user-avatar.data-v-6ae23533 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #2E8B57;
}
.user-header .user-info .avatar-section .status-badge.data-v-6ae23533 {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  color: #fff;
}
.user-header .user-info .avatar-section .status-badge.not_submitted.data-v-6ae23533 {
  background: #FF9500;
}
.user-header .user-info .avatar-section .status-badge.pending.data-v-6ae23533 {
  background: #4A90E2;
}
.user-header .user-info .avatar-section .status-badge.approved.data-v-6ae23533 {
  background: #4CAF50;
}
.user-header .user-info .avatar-section .status-badge.rejected.data-v-6ae23533 {
  background: #f56c6c;
}
.user-header .user-info .info-section.data-v-6ae23533 {
  flex: 1;
}
.user-header .user-info .info-section .user-name.data-v-6ae23533 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.user-header .user-info .info-section .user-org.data-v-6ae23533 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.user-header .user-info .info-section .user-meta .join-date.data-v-6ae23533 {
  font-size: 24rpx;
  color: #999;
}
.user-header .user-info .action-section.data-v-6ae23533 {
  width: 60rpx;
  height: 60rpx;
  background: #2E8B57;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.stats-section.data-v-6ae23533 {
  margin: 40rpx 30rpx;
}
.stats-section .stats-card.data-v-6ae23533 {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
}
.stats-section .stats-card .stat-item.data-v-6ae23533 {
  flex: 1;
  text-align: center;
}
.stats-section .stats-card .stat-item .stat-number.data-v-6ae23533 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2E8B57;
  margin-bottom: 8rpx;
}
.stats-section .stats-card .stat-item .stat-label.data-v-6ae23533 {
  font-size: 24rpx;
  color: #666;
}
.stats-section .stats-card .stat-divider.data-v-6ae23533 {
  width: 2rpx;
  height: 60rpx;
  background: #f0f0f0;
}
.menu-section.data-v-6ae23533 {
  padding: 0 30rpx 120rpx;
}
.menu-section .menu-group.data-v-6ae23533 {
  margin-bottom: 40rpx;
}
.menu-section .menu-group .group-title.data-v-6ae23533 {
  margin-bottom: 20rpx;
}
.menu-section .menu-group .group-title text.data-v-6ae23533 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.menu-section .menu-group .menu-list.data-v-6ae23533 {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}
.menu-section .menu-group .menu-list .menu-item.data-v-6ae23533 {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
}
.menu-section .menu-group .menu-list .menu-item.data-v-6ae23533:last-child {
  border-bottom: none;
}
.menu-section .menu-group .menu-list .menu-item .menu-icon.data-v-6ae23533 {
  width: 80rpx;
  height: 80rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.menu-section .menu-group .menu-list .menu-item .menu-content.data-v-6ae23533 {
  flex: 1;
}
.menu-section .menu-group .menu-list .menu-item .menu-content .menu-title.data-v-6ae23533 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.menu-section .menu-group .menu-list .menu-item .menu-content .menu-desc.data-v-6ae23533 {
  font-size: 24rpx;
  color: #666;
}
.menu-section .menu-group .menu-list .menu-item .menu-badge.data-v-6ae23533 {
  width: 40rpx;
  height: 40rpx;
  background: #f56c6c;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.menu-section .menu-group .menu-list .menu-item .menu-badge text.data-v-6ae23533 {
  font-size: 20rpx;
  color: #fff;
  font-weight: bold;
}