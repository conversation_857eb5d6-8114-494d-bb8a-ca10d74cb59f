/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-dd383ca2 {
  box-sizing: border-box;
}
page.data-v-dd383ca2 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-dd383ca2 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-dd383ca2 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-dd383ca2 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-dd383ca2 {
  display: flex;
}
.flex-column.data-v-dd383ca2 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-dd383ca2 {
  flex: 1;
}
.flex-wrap.data-v-dd383ca2 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-dd383ca2 {
  color: #262626;
}
.text-secondary.data-v-dd383ca2 {
  color: #595959;
}
.text-disabled.data-v-dd383ca2 {
  color: #BFBFBF;
}
.text-success.data-v-dd383ca2 {
  color: #52C41A;
}
.text-warning.data-v-dd383ca2 {
  color: #FAAD14;
}
.text-error.data-v-dd383ca2 {
  color: #F5222D;
}
.text-primary-color.data-v-dd383ca2 {
  color: #2E8B57;
}
.text-center.data-v-dd383ca2 {
  text-align: center;
}
.text-left.data-v-dd383ca2 {
  text-align: left;
}
.text-right.data-v-dd383ca2 {
  text-align: right;
}
.text-bold.data-v-dd383ca2 {
  font-weight: bold;
}
.text-normal.data-v-dd383ca2 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-dd383ca2 {
  font-size: 20rpx;
}
.text-sm.data-v-dd383ca2 {
  font-size: 24rpx;
}
.text-base.data-v-dd383ca2 {
  font-size: 28rpx;
}
.text-lg.data-v-dd383ca2 {
  font-size: 32rpx;
}
.text-xl.data-v-dd383ca2 {
  font-size: 36rpx;
}
.text-2xl.data-v-dd383ca2 {
  font-size: 40rpx;
}
.text-3xl.data-v-dd383ca2 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-dd383ca2 {
  margin: 0;
}
.m-1.data-v-dd383ca2 {
  margin: 8rpx;
}
.m-2.data-v-dd383ca2 {
  margin: 16rpx;
}
.m-3.data-v-dd383ca2 {
  margin: 24rpx;
}
.m-4.data-v-dd383ca2 {
  margin: 32rpx;
}
.m-5.data-v-dd383ca2 {
  margin: 40rpx;
}
.mt-0.data-v-dd383ca2 {
  margin-top: 0;
}
.mt-1.data-v-dd383ca2 {
  margin-top: 8rpx;
}
.mt-2.data-v-dd383ca2 {
  margin-top: 16rpx;
}
.mt-3.data-v-dd383ca2 {
  margin-top: 24rpx;
}
.mt-4.data-v-dd383ca2 {
  margin-top: 32rpx;
}
.mt-5.data-v-dd383ca2 {
  margin-top: 40rpx;
}
.mb-0.data-v-dd383ca2 {
  margin-bottom: 0;
}
.mb-1.data-v-dd383ca2 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-dd383ca2 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-dd383ca2 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-dd383ca2 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-dd383ca2 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-dd383ca2 {
  margin-left: 0;
}
.ml-1.data-v-dd383ca2 {
  margin-left: 8rpx;
}
.ml-2.data-v-dd383ca2 {
  margin-left: 16rpx;
}
.ml-3.data-v-dd383ca2 {
  margin-left: 24rpx;
}
.ml-4.data-v-dd383ca2 {
  margin-left: 32rpx;
}
.ml-5.data-v-dd383ca2 {
  margin-left: 40rpx;
}
.mr-0.data-v-dd383ca2 {
  margin-right: 0;
}
.mr-1.data-v-dd383ca2 {
  margin-right: 8rpx;
}
.mr-2.data-v-dd383ca2 {
  margin-right: 16rpx;
}
.mr-3.data-v-dd383ca2 {
  margin-right: 24rpx;
}
.mr-4.data-v-dd383ca2 {
  margin-right: 32rpx;
}
.mr-5.data-v-dd383ca2 {
  margin-right: 40rpx;
}
.p-0.data-v-dd383ca2 {
  padding: 0;
}
.p-1.data-v-dd383ca2 {
  padding: 8rpx;
}
.p-2.data-v-dd383ca2 {
  padding: 16rpx;
}
.p-3.data-v-dd383ca2 {
  padding: 24rpx;
}
.p-4.data-v-dd383ca2 {
  padding: 32rpx;
}
.p-5.data-v-dd383ca2 {
  padding: 40rpx;
}
.pt-0.data-v-dd383ca2 {
  padding-top: 0;
}
.pt-1.data-v-dd383ca2 {
  padding-top: 8rpx;
}
.pt-2.data-v-dd383ca2 {
  padding-top: 16rpx;
}
.pt-3.data-v-dd383ca2 {
  padding-top: 24rpx;
}
.pt-4.data-v-dd383ca2 {
  padding-top: 32rpx;
}
.pt-5.data-v-dd383ca2 {
  padding-top: 40rpx;
}
.pb-0.data-v-dd383ca2 {
  padding-bottom: 0;
}
.pb-1.data-v-dd383ca2 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-dd383ca2 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-dd383ca2 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-dd383ca2 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-dd383ca2 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-dd383ca2 {
  padding-left: 0;
}
.pl-1.data-v-dd383ca2 {
  padding-left: 8rpx;
}
.pl-2.data-v-dd383ca2 {
  padding-left: 16rpx;
}
.pl-3.data-v-dd383ca2 {
  padding-left: 24rpx;
}
.pl-4.data-v-dd383ca2 {
  padding-left: 32rpx;
}
.pl-5.data-v-dd383ca2 {
  padding-left: 40rpx;
}
.pr-0.data-v-dd383ca2 {
  padding-right: 0;
}
.pr-1.data-v-dd383ca2 {
  padding-right: 8rpx;
}
.pr-2.data-v-dd383ca2 {
  padding-right: 16rpx;
}
.pr-3.data-v-dd383ca2 {
  padding-right: 24rpx;
}
.pr-4.data-v-dd383ca2 {
  padding-right: 32rpx;
}
.pr-5.data-v-dd383ca2 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-dd383ca2 {
  width: 100%;
}
.h-full.data-v-dd383ca2 {
  height: 100%;
}
.w-screen.data-v-dd383ca2 {
  width: 100vw;
}
.h-screen.data-v-dd383ca2 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-dd383ca2 {
  border-radius: 0;
}
.rounded-sm.data-v-dd383ca2 {
  border-radius: 4rpx;
}
.rounded.data-v-dd383ca2 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-dd383ca2 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-dd383ca2 {
  border-radius: 24rpx;
}
.rounded-full.data-v-dd383ca2 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-dd383ca2 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-dd383ca2 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-dd383ca2 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-dd383ca2 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-dd383ca2 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-dd383ca2 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-dd383ca2 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-dd383ca2 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-dd383ca2 {
  background-color: #2E8B57;
}
.bg-light.data-v-dd383ca2 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-dd383ca2 {
  background-color: #F8F9FA;
}
.bg-success.data-v-dd383ca2 {
  background-color: #52C41A;
}
.bg-warning.data-v-dd383ca2 {
  background-color: #FAAD14;
}
.bg-error.data-v-dd383ca2 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-dd383ca2 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-dd383ca2 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-dd383ca2 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-dd383ca2 {
  color: #FAAD14;
}
.status-approved.data-v-dd383ca2 {
  color: #52C41A;
}
.status-rejected.data-v-dd383ca2 {
  color: #F5222D;
}
.status-not-submitted.data-v-dd383ca2 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-dd383ca2 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-dd383ca2 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-dd383ca2 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-dd383ca2 {
  animation: fadeIn-dd383ca2 0.3s ease-in-out;
}
@keyframes fadeIn-dd383ca2 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-dd383ca2 {
  animation: slideUp-dd383ca2 0.3s ease-out;
}
@keyframes slideUp-dd383ca2 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.profile-submit-container.data-v-dd383ca2 {
  min-height: 100vh;
  background: #F8F9FA;
}
.main-content.data-v-dd383ca2 {
  padding: 40rpx 30rpx;
}
.progress-section.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  padding: 40rpx 20rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.progress-section .progress-item.data-v-dd383ca2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.progress-section .progress-item .progress-icon.data-v-dd383ca2 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  background: #e9ecef;
  color: #6c757d;
}
.progress-section .progress-item .progress-text.data-v-dd383ca2 {
  font-size: 24rpx;
  color: #6c757d;
}
.progress-section .progress-item.active .progress-icon.data-v-dd383ca2 {
  background: #2E8B57;
  color: #fff;
}
.progress-section .progress-item.active .progress-text.data-v-dd383ca2 {
  color: #2E8B57;
  font-weight: bold;
}
.progress-section .progress-item.current .progress-icon.data-v-dd383ca2 {
  background: #FAAD14;
  color: #fff;
}
.progress-section .progress-item.current .progress-text.data-v-dd383ca2 {
  color: #FAAD14;
  font-weight: bold;
}
.progress-section .progress-line.data-v-dd383ca2 {
  width: 60rpx;
  height: 4rpx;
  background: #e9ecef;
  margin: 0 20rpx;
  margin-bottom: 30rpx;
}
.form-container.data-v-dd383ca2 {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.form-section.data-v-dd383ca2 {
  margin-bottom: 60rpx;
}
.form-section.data-v-dd383ca2:last-child {
  margin-bottom: 40rpx;
}
.form-section .section-title.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.form-section .section-title text.data-v-dd383ca2 {
  margin-left: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #262626;
}
.photo-upload-section .upload-tips.data-v-dd383ca2 {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #fff7e6;
  border-radius: 16rpx;
  border-left: 6rpx solid #FAAD14;
  margin-bottom: 40rpx;
}
.photo-upload-section .upload-tips .tips-text.data-v-dd383ca2 {
  flex: 1;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-left: 12rpx;
}
.photo-upload-section .upload-area.data-v-dd383ca2 {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.photo-upload-section .upload-area .upload-placeholder.data-v-dd383ca2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.photo-upload-section .upload-area .upload-placeholder .upload-text.data-v-dd383ca2 {
  font-size: 24rpx;
  color: #999;
  margin: 16rpx 0 8rpx;
}
.photo-upload-section .upload-area .upload-placeholder .upload-size.data-v-dd383ca2 {
  font-size: 20rpx;
  color: #ccc;
}
.photo-upload-section .upload-area .uploaded-image.data-v-dd383ca2 {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.photo-upload-section .upload-area .image-actions.data-v-dd383ca2 {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  display: flex;
  gap: 8rpx;
}
.photo-upload-section .upload-area .image-actions .action-btn.data-v-dd383ca2 {
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-buttons.data-v-dd383ca2 {
  display: flex;
  gap: 24rpx;
  margin-top: 60rpx;
}
.action-buttons .skip-btn.data-v-dd383ca2 {
  flex: 1;
}
.action-buttons .submit-btn.data-v-dd383ca2 {
  flex: 2;
}