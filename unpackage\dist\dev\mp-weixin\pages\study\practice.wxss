/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-17cd85ad {
  box-sizing: border-box;
}
page.data-v-17cd85ad {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-17cd85ad {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-17cd85ad {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-17cd85ad {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-17cd85ad {
  display: flex;
}
.flex-column.data-v-17cd85ad {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-17cd85ad {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-17cd85ad {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-17cd85ad {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-17cd85ad {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-17cd85ad {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-17cd85ad {
  flex: 1;
}
.flex-wrap.data-v-17cd85ad {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-17cd85ad {
  color: #262626;
}
.text-secondary.data-v-17cd85ad {
  color: #595959;
}
.text-disabled.data-v-17cd85ad {
  color: #BFBFBF;
}
.text-success.data-v-17cd85ad {
  color: #52C41A;
}
.text-warning.data-v-17cd85ad {
  color: #FAAD14;
}
.text-error.data-v-17cd85ad {
  color: #F5222D;
}
.text-primary-color.data-v-17cd85ad {
  color: #2E8B57;
}
.text-center.data-v-17cd85ad {
  text-align: center;
}
.text-left.data-v-17cd85ad {
  text-align: left;
}
.text-right.data-v-17cd85ad {
  text-align: right;
}
.text-bold.data-v-17cd85ad {
  font-weight: bold;
}
.text-normal.data-v-17cd85ad {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-17cd85ad {
  font-size: 20rpx;
}
.text-sm.data-v-17cd85ad {
  font-size: 24rpx;
}
.text-base.data-v-17cd85ad {
  font-size: 28rpx;
}
.text-lg.data-v-17cd85ad {
  font-size: 32rpx;
}
.text-xl.data-v-17cd85ad {
  font-size: 36rpx;
}
.text-2xl.data-v-17cd85ad {
  font-size: 40rpx;
}
.text-3xl.data-v-17cd85ad {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-17cd85ad {
  margin: 0;
}
.m-1.data-v-17cd85ad {
  margin: 8rpx;
}
.m-2.data-v-17cd85ad {
  margin: 16rpx;
}
.m-3.data-v-17cd85ad {
  margin: 24rpx;
}
.m-4.data-v-17cd85ad {
  margin: 32rpx;
}
.m-5.data-v-17cd85ad {
  margin: 40rpx;
}
.mt-0.data-v-17cd85ad {
  margin-top: 0;
}
.mt-1.data-v-17cd85ad {
  margin-top: 8rpx;
}
.mt-2.data-v-17cd85ad {
  margin-top: 16rpx;
}
.mt-3.data-v-17cd85ad {
  margin-top: 24rpx;
}
.mt-4.data-v-17cd85ad {
  margin-top: 32rpx;
}
.mt-5.data-v-17cd85ad {
  margin-top: 40rpx;
}
.mb-0.data-v-17cd85ad {
  margin-bottom: 0;
}
.mb-1.data-v-17cd85ad {
  margin-bottom: 8rpx;
}
.mb-2.data-v-17cd85ad {
  margin-bottom: 16rpx;
}
.mb-3.data-v-17cd85ad {
  margin-bottom: 24rpx;
}
.mb-4.data-v-17cd85ad {
  margin-bottom: 32rpx;
}
.mb-5.data-v-17cd85ad {
  margin-bottom: 40rpx;
}
.ml-0.data-v-17cd85ad {
  margin-left: 0;
}
.ml-1.data-v-17cd85ad {
  margin-left: 8rpx;
}
.ml-2.data-v-17cd85ad {
  margin-left: 16rpx;
}
.ml-3.data-v-17cd85ad {
  margin-left: 24rpx;
}
.ml-4.data-v-17cd85ad {
  margin-left: 32rpx;
}
.ml-5.data-v-17cd85ad {
  margin-left: 40rpx;
}
.mr-0.data-v-17cd85ad {
  margin-right: 0;
}
.mr-1.data-v-17cd85ad {
  margin-right: 8rpx;
}
.mr-2.data-v-17cd85ad {
  margin-right: 16rpx;
}
.mr-3.data-v-17cd85ad {
  margin-right: 24rpx;
}
.mr-4.data-v-17cd85ad {
  margin-right: 32rpx;
}
.mr-5.data-v-17cd85ad {
  margin-right: 40rpx;
}
.p-0.data-v-17cd85ad {
  padding: 0;
}
.p-1.data-v-17cd85ad {
  padding: 8rpx;
}
.p-2.data-v-17cd85ad {
  padding: 16rpx;
}
.p-3.data-v-17cd85ad {
  padding: 24rpx;
}
.p-4.data-v-17cd85ad {
  padding: 32rpx;
}
.p-5.data-v-17cd85ad {
  padding: 40rpx;
}
.pt-0.data-v-17cd85ad {
  padding-top: 0;
}
.pt-1.data-v-17cd85ad {
  padding-top: 8rpx;
}
.pt-2.data-v-17cd85ad {
  padding-top: 16rpx;
}
.pt-3.data-v-17cd85ad {
  padding-top: 24rpx;
}
.pt-4.data-v-17cd85ad {
  padding-top: 32rpx;
}
.pt-5.data-v-17cd85ad {
  padding-top: 40rpx;
}
.pb-0.data-v-17cd85ad {
  padding-bottom: 0;
}
.pb-1.data-v-17cd85ad {
  padding-bottom: 8rpx;
}
.pb-2.data-v-17cd85ad {
  padding-bottom: 16rpx;
}
.pb-3.data-v-17cd85ad {
  padding-bottom: 24rpx;
}
.pb-4.data-v-17cd85ad {
  padding-bottom: 32rpx;
}
.pb-5.data-v-17cd85ad {
  padding-bottom: 40rpx;
}
.pl-0.data-v-17cd85ad {
  padding-left: 0;
}
.pl-1.data-v-17cd85ad {
  padding-left: 8rpx;
}
.pl-2.data-v-17cd85ad {
  padding-left: 16rpx;
}
.pl-3.data-v-17cd85ad {
  padding-left: 24rpx;
}
.pl-4.data-v-17cd85ad {
  padding-left: 32rpx;
}
.pl-5.data-v-17cd85ad {
  padding-left: 40rpx;
}
.pr-0.data-v-17cd85ad {
  padding-right: 0;
}
.pr-1.data-v-17cd85ad {
  padding-right: 8rpx;
}
.pr-2.data-v-17cd85ad {
  padding-right: 16rpx;
}
.pr-3.data-v-17cd85ad {
  padding-right: 24rpx;
}
.pr-4.data-v-17cd85ad {
  padding-right: 32rpx;
}
.pr-5.data-v-17cd85ad {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-17cd85ad {
  width: 100%;
}
.h-full.data-v-17cd85ad {
  height: 100%;
}
.w-screen.data-v-17cd85ad {
  width: 100vw;
}
.h-screen.data-v-17cd85ad {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-17cd85ad {
  border-radius: 0;
}
.rounded-sm.data-v-17cd85ad {
  border-radius: 4rpx;
}
.rounded.data-v-17cd85ad {
  border-radius: 8rpx;
}
.rounded-lg.data-v-17cd85ad {
  border-radius: 16rpx;
}
.rounded-xl.data-v-17cd85ad {
  border-radius: 24rpx;
}
.rounded-full.data-v-17cd85ad {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-17cd85ad {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-17cd85ad {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-17cd85ad {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-17cd85ad {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-17cd85ad {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-17cd85ad {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-17cd85ad {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-17cd85ad {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-17cd85ad {
  background-color: #2E8B57;
}
.bg-light.data-v-17cd85ad {
  background-color: #FFFFFF;
}
.bg-gray.data-v-17cd85ad {
  background-color: #F8F9FA;
}
.bg-success.data-v-17cd85ad {
  background-color: #52C41A;
}
.bg-warning.data-v-17cd85ad {
  background-color: #FAAD14;
}
.bg-error.data-v-17cd85ad {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-17cd85ad {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-17cd85ad {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-17cd85ad {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-17cd85ad {
  color: #FAAD14;
}
.status-approved.data-v-17cd85ad {
  color: #52C41A;
}
.status-rejected.data-v-17cd85ad {
  color: #F5222D;
}
.status-not-submitted.data-v-17cd85ad {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-17cd85ad {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-17cd85ad {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-17cd85ad {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-17cd85ad {
  animation: fadeIn-17cd85ad 0.3s ease-in-out;
}
@keyframes fadeIn-17cd85ad {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-17cd85ad {
  animation: slideUp-17cd85ad 0.3s ease-out;
}
@keyframes slideUp-17cd85ad {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.practice-container.data-v-17cd85ad {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 120rpx;
}
.practice-progress.data-v-17cd85ad {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}
.practice-progress .progress-info.data-v-17cd85ad {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.practice-progress .progress-info .current-question.data-v-17cd85ad {
  font-size: 32rpx;
  font-weight: bold;
  color: #4A90E2;
}
.practice-progress .progress-info .total-questions.data-v-17cd85ad {
  font-size: 26rpx;
  color: #666;
}
.practice-progress .progress-bar.data-v-17cd85ad {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.practice-progress .progress-bar .progress-fill.data-v-17cd85ad {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
  transition: width 0.3s ease;
}
.practice-progress .time-info.data-v-17cd85ad {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.practice-progress .time-info .elapsed-time.data-v-17cd85ad {
  font-size: 24rpx;
  color: #666;
}
.question-container.data-v-17cd85ad {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.question-container .question-header.data-v-17cd85ad {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.question-container .question-header .question-difficulty .difficulty-easy.data-v-17cd85ad {
  color: #4CAF50;
  font-size: 24rpx;
  font-weight: bold;
}
.question-container .question-header .question-difficulty .difficulty-medium.data-v-17cd85ad {
  color: #FF9500;
  font-size: 24rpx;
  font-weight: bold;
}
.question-container .question-header .question-difficulty .difficulty-hard.data-v-17cd85ad {
  color: #f56c6c;
  font-size: 24rpx;
  font-weight: bold;
}
.question-container .question-content.data-v-17cd85ad {
  margin-bottom: 40rpx;
}
.question-container .question-content .question-title.data-v-17cd85ad {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  font-weight: 500;
}
.question-container .question-options .option-item.data-v-17cd85ad {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.question-container .question-options .option-item.selected.data-v-17cd85ad {
  border-color: #4A90E2;
  background: rgba(74, 144, 226, 0.05);
}
.question-container .question-options .option-item .option-indicator.data-v-17cd85ad {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  margin-right: 24rpx;
}
.question-container .question-options .option-item .option-indicator .option-label.data-v-17cd85ad {
  font-size: 28rpx;
  font-weight: bold;
  color: #4A90E2;
}
.question-container .question-options .option-item .option-text.data-v-17cd85ad {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}
.question-container .judge-options.data-v-17cd85ad {
  display: flex;
  gap: 40rpx;
  justify-content: center;
}
.question-container .judge-options .judge-option.data-v-17cd85ad {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 40rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 20rpx;
  flex: 1;
  transition: all 0.3s ease;
}
.question-container .judge-options .judge-option.active.data-v-17cd85ad {
  border-color: #4A90E2;
  background: rgba(74, 144, 226, 0.05);
}
.question-container .judge-options .judge-option text.data-v-17cd85ad {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.question-container .essay-input.data-v-17cd85ad {
  margin-top: 20rpx;
}
.action-buttons.data-v-17cd85ad {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 30rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
}
.action-buttons .prev-btn.data-v-17cd85ad {
  flex: 1;
}
.action-buttons .next-btn.data-v-17cd85ad {
  flex: 2;
}
.answer-sheet-btn.data-v-17cd85ad {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 120rpx;
  height: 120rpx;
  background: #fff;
  border-radius: 60rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}
.answer-sheet-btn text.data-v-17cd85ad {
  font-size: 20rpx;
  color: #4A90E2;
  font-weight: bold;
}
.answer-sheet-modal.data-v-17cd85ad {
  padding: 40rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.answer-sheet-modal .modal-header.data-v-17cd85ad {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.answer-sheet-modal .modal-header .modal-title.data-v-17cd85ad {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.answer-sheet-modal .answer-grid.data-v-17cd85ad {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
  flex: 1;
  margin-bottom: 40rpx;
}
.answer-sheet-modal .answer-grid .answer-item.data-v-17cd85ad {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.answer-sheet-modal .answer-grid .answer-item.answered.data-v-17cd85ad {
  background: #4CAF50;
  color: #fff;
}
.answer-sheet-modal .answer-grid .answer-item.current.data-v-17cd85ad {
  background: #4A90E2;
  color: #fff;
}
.answer-sheet-modal .answer-grid .answer-item.unanswered.data-v-17cd85ad {
  background: #f0f0f0;
  color: #999;
}
.answer-sheet-modal .answer-legend.data-v-17cd85ad {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}
.answer-sheet-modal .answer-legend .legend-item.data-v-17cd85ad {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.answer-sheet-modal .answer-legend .legend-item .legend-color.data-v-17cd85ad {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
}
.answer-sheet-modal .answer-legend .legend-item .legend-color.answered.data-v-17cd85ad {
  background: #4CAF50;
}
.answer-sheet-modal .answer-legend .legend-item .legend-color.current.data-v-17cd85ad {
  background: #4A90E2;
}
.answer-sheet-modal .answer-legend .legend-item .legend-color.unanswered.data-v-17cd85ad {
  background: #f0f0f0;
}
.answer-sheet-modal .answer-legend .legend-item text.data-v-17cd85ad {
  font-size: 24rpx;
  color: #666;
}