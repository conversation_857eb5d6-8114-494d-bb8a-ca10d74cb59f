/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-3f273c1e {
  box-sizing: border-box;
}
page.data-v-3f273c1e {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-3f273c1e {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-3f273c1e {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-3f273c1e {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-3f273c1e {
  display: flex;
}
.flex-column.data-v-3f273c1e {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-3f273c1e {
  flex: 1;
}
.flex-wrap.data-v-3f273c1e {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-3f273c1e {
  color: #262626;
}
.text-secondary.data-v-3f273c1e {
  color: #595959;
}
.text-disabled.data-v-3f273c1e {
  color: #BFBFBF;
}
.text-success.data-v-3f273c1e {
  color: #52C41A;
}
.text-warning.data-v-3f273c1e {
  color: #FAAD14;
}
.text-error.data-v-3f273c1e {
  color: #F5222D;
}
.text-primary-color.data-v-3f273c1e {
  color: #2E8B57;
}
.text-center.data-v-3f273c1e {
  text-align: center;
}
.text-left.data-v-3f273c1e {
  text-align: left;
}
.text-right.data-v-3f273c1e {
  text-align: right;
}
.text-bold.data-v-3f273c1e {
  font-weight: bold;
}
.text-normal.data-v-3f273c1e {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-3f273c1e {
  font-size: 20rpx;
}
.text-sm.data-v-3f273c1e {
  font-size: 24rpx;
}
.text-base.data-v-3f273c1e {
  font-size: 28rpx;
}
.text-lg.data-v-3f273c1e {
  font-size: 32rpx;
}
.text-xl.data-v-3f273c1e {
  font-size: 36rpx;
}
.text-2xl.data-v-3f273c1e {
  font-size: 40rpx;
}
.text-3xl.data-v-3f273c1e {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-3f273c1e {
  margin: 0;
}
.m-1.data-v-3f273c1e {
  margin: 8rpx;
}
.m-2.data-v-3f273c1e {
  margin: 16rpx;
}
.m-3.data-v-3f273c1e {
  margin: 24rpx;
}
.m-4.data-v-3f273c1e {
  margin: 32rpx;
}
.m-5.data-v-3f273c1e {
  margin: 40rpx;
}
.mt-0.data-v-3f273c1e {
  margin-top: 0;
}
.mt-1.data-v-3f273c1e {
  margin-top: 8rpx;
}
.mt-2.data-v-3f273c1e {
  margin-top: 16rpx;
}
.mt-3.data-v-3f273c1e {
  margin-top: 24rpx;
}
.mt-4.data-v-3f273c1e {
  margin-top: 32rpx;
}
.mt-5.data-v-3f273c1e {
  margin-top: 40rpx;
}
.mb-0.data-v-3f273c1e {
  margin-bottom: 0;
}
.mb-1.data-v-3f273c1e {
  margin-bottom: 8rpx;
}
.mb-2.data-v-3f273c1e {
  margin-bottom: 16rpx;
}
.mb-3.data-v-3f273c1e {
  margin-bottom: 24rpx;
}
.mb-4.data-v-3f273c1e {
  margin-bottom: 32rpx;
}
.mb-5.data-v-3f273c1e {
  margin-bottom: 40rpx;
}
.ml-0.data-v-3f273c1e {
  margin-left: 0;
}
.ml-1.data-v-3f273c1e {
  margin-left: 8rpx;
}
.ml-2.data-v-3f273c1e {
  margin-left: 16rpx;
}
.ml-3.data-v-3f273c1e {
  margin-left: 24rpx;
}
.ml-4.data-v-3f273c1e {
  margin-left: 32rpx;
}
.ml-5.data-v-3f273c1e {
  margin-left: 40rpx;
}
.mr-0.data-v-3f273c1e {
  margin-right: 0;
}
.mr-1.data-v-3f273c1e {
  margin-right: 8rpx;
}
.mr-2.data-v-3f273c1e {
  margin-right: 16rpx;
}
.mr-3.data-v-3f273c1e {
  margin-right: 24rpx;
}
.mr-4.data-v-3f273c1e {
  margin-right: 32rpx;
}
.mr-5.data-v-3f273c1e {
  margin-right: 40rpx;
}
.p-0.data-v-3f273c1e {
  padding: 0;
}
.p-1.data-v-3f273c1e {
  padding: 8rpx;
}
.p-2.data-v-3f273c1e {
  padding: 16rpx;
}
.p-3.data-v-3f273c1e {
  padding: 24rpx;
}
.p-4.data-v-3f273c1e {
  padding: 32rpx;
}
.p-5.data-v-3f273c1e {
  padding: 40rpx;
}
.pt-0.data-v-3f273c1e {
  padding-top: 0;
}
.pt-1.data-v-3f273c1e {
  padding-top: 8rpx;
}
.pt-2.data-v-3f273c1e {
  padding-top: 16rpx;
}
.pt-3.data-v-3f273c1e {
  padding-top: 24rpx;
}
.pt-4.data-v-3f273c1e {
  padding-top: 32rpx;
}
.pt-5.data-v-3f273c1e {
  padding-top: 40rpx;
}
.pb-0.data-v-3f273c1e {
  padding-bottom: 0;
}
.pb-1.data-v-3f273c1e {
  padding-bottom: 8rpx;
}
.pb-2.data-v-3f273c1e {
  padding-bottom: 16rpx;
}
.pb-3.data-v-3f273c1e {
  padding-bottom: 24rpx;
}
.pb-4.data-v-3f273c1e {
  padding-bottom: 32rpx;
}
.pb-5.data-v-3f273c1e {
  padding-bottom: 40rpx;
}
.pl-0.data-v-3f273c1e {
  padding-left: 0;
}
.pl-1.data-v-3f273c1e {
  padding-left: 8rpx;
}
.pl-2.data-v-3f273c1e {
  padding-left: 16rpx;
}
.pl-3.data-v-3f273c1e {
  padding-left: 24rpx;
}
.pl-4.data-v-3f273c1e {
  padding-left: 32rpx;
}
.pl-5.data-v-3f273c1e {
  padding-left: 40rpx;
}
.pr-0.data-v-3f273c1e {
  padding-right: 0;
}
.pr-1.data-v-3f273c1e {
  padding-right: 8rpx;
}
.pr-2.data-v-3f273c1e {
  padding-right: 16rpx;
}
.pr-3.data-v-3f273c1e {
  padding-right: 24rpx;
}
.pr-4.data-v-3f273c1e {
  padding-right: 32rpx;
}
.pr-5.data-v-3f273c1e {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-3f273c1e {
  width: 100%;
}
.h-full.data-v-3f273c1e {
  height: 100%;
}
.w-screen.data-v-3f273c1e {
  width: 100vw;
}
.h-screen.data-v-3f273c1e {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-3f273c1e {
  border-radius: 0;
}
.rounded-sm.data-v-3f273c1e {
  border-radius: 4rpx;
}
.rounded.data-v-3f273c1e {
  border-radius: 8rpx;
}
.rounded-lg.data-v-3f273c1e {
  border-radius: 16rpx;
}
.rounded-xl.data-v-3f273c1e {
  border-radius: 24rpx;
}
.rounded-full.data-v-3f273c1e {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-3f273c1e {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-3f273c1e {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-3f273c1e {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-3f273c1e {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-3f273c1e {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-3f273c1e {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-3f273c1e {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-3f273c1e {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-3f273c1e {
  background-color: #2E8B57;
}
.bg-light.data-v-3f273c1e {
  background-color: #FFFFFF;
}
.bg-gray.data-v-3f273c1e {
  background-color: #F8F9FA;
}
.bg-success.data-v-3f273c1e {
  background-color: #52C41A;
}
.bg-warning.data-v-3f273c1e {
  background-color: #FAAD14;
}
.bg-error.data-v-3f273c1e {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-3f273c1e {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-3f273c1e {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-3f273c1e {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-3f273c1e {
  color: #FAAD14;
}
.status-approved.data-v-3f273c1e {
  color: #52C41A;
}
.status-rejected.data-v-3f273c1e {
  color: #F5222D;
}
.status-not-submitted.data-v-3f273c1e {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-3f273c1e {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-3f273c1e {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-3f273c1e {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-3f273c1e {
  animation: fadeIn-3f273c1e 0.3s ease-in-out;
}
@keyframes fadeIn-3f273c1e {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-3f273c1e {
  animation: slideUp-3f273c1e 0.3s ease-out;
}
@keyframes slideUp-3f273c1e {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.study-center-container.data-v-3f273c1e {
  min-height: 100vh;
  background: #F8F9FA;
}
.status-bar.data-v-3f273c1e {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}
.header.data-v-3f273c1e {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;
}
.header .header-content.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header .header-content .page-title.data-v-3f273c1e {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}
.header .header-content .header-actions .study-stats .stats-text.data-v-3f273c1e {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.main-content.data-v-3f273c1e {
  padding: 30rpx;
  padding-bottom: 120rpx;
}
.stats-card.data-v-3f273c1e {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}
.stats-card .stats-item.data-v-3f273c1e {
  flex: 1;
  text-align: center;
}
.stats-card .stats-item .stats-number.data-v-3f273c1e {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 8rpx;
}
.stats-card .stats-item .stats-label.data-v-3f273c1e {
  font-size: 24rpx;
  color: #666;
}
.stats-card .stats-divider.data-v-3f273c1e {
  width: 2rpx;
  height: 60rpx;
  background: #f0f0f0;
}
.study-modules.data-v-3f273c1e {
  margin-bottom: 40rpx;
}
.study-modules .module-card.data-v-3f273c1e {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
}
.study-modules .module-card.disabled.data-v-3f273c1e {
  opacity: 0.6;
}
.study-modules .module-card.disabled .module-content .module-status.data-v-3f273c1e {
  color: #999;
  font-size: 24rpx;
}
.study-modules .module-card.login-prompt.data-v-3f273c1e {
  border: 2rpx dashed #2E8B57;
  background: rgba(46, 139, 87, 0.05);
}
.study-modules .module-card .module-icon.data-v-3f273c1e {
  position: relative;
  margin-right: 40rpx;
}
.study-modules .module-card .module-icon.textbook.data-v-3f273c1e {
  padding: 24rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 20rpx;
}
.study-modules .module-card .module-icon.question-bank.data-v-3f273c1e {
  padding: 24rpx;
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
  border-radius: 20rpx;
}
.study-modules .module-card .module-icon.login.data-v-3f273c1e {
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(46, 139, 87, 0.1) 0%, rgba(46, 139, 87, 0.2) 100%);
  border-radius: 20rpx;
}
.study-modules .module-card .module-icon .coming-soon-badge.data-v-3f273c1e,
.study-modules .module-card .module-icon .limit-badge.data-v-3f273c1e {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
}
.study-modules .module-card .module-icon .coming-soon-badge text.data-v-3f273c1e,
.study-modules .module-card .module-icon .limit-badge text.data-v-3f273c1e {
  font-size: 18rpx;
  color: #fff;
  font-weight: bold;
}
.study-modules .module-card .module-icon .coming-soon-badge.data-v-3f273c1e {
  background: #FF9500;
}
.study-modules .module-card .module-icon .limit-badge.data-v-3f273c1e {
  background: #f56c6c;
}
.study-modules .module-card .module-content.data-v-3f273c1e {
  flex: 1;
}
.study-modules .module-card .module-content .module-title.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.study-modules .module-card .module-content .module-desc.data-v-3f273c1e {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.study-modules .module-card .module-content .module-info.data-v-3f273c1e {
  display: block;
  font-size: 24rpx;
  color: #4A90E2;
}
.study-modules .module-card .module-content .module-status.data-v-3f273c1e {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.vip-preview.data-v-3f273c1e {
  margin-bottom: 40rpx;
}
.vip-preview .vip-card.data-v-3f273c1e {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  color: #fff;
}
.vip-preview .vip-card .vip-header.data-v-3f273c1e {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.vip-preview .vip-card .vip-header .vip-icon.data-v-3f273c1e {
  margin-right: 24rpx;
}
.vip-preview .vip-card .vip-header .vip-content.data-v-3f273c1e {
  flex: 1;
}
.vip-preview .vip-card .vip-header .vip-content .vip-title.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}
.vip-preview .vip-card .vip-header .vip-content .vip-desc.data-v-3f273c1e {
  font-size: 24rpx;
  opacity: 0.8;
}
.vip-preview .vip-card .vip-features.data-v-3f273c1e {
  margin-bottom: 40rpx;
}
.vip-preview .vip-card .vip-features .feature-item.data-v-3f273c1e {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.vip-preview .vip-card .vip-features .feature-item.data-v-3f273c1e:last-child {
  margin-bottom: 0;
}
.vip-preview .vip-card .vip-features .feature-item text.data-v-3f273c1e {
  margin-left: 16rpx;
  font-size: 26rpx;
}
.vip-preview .vip-card .vip-btn.data-v-3f273c1e {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}
.recent-practices .section-header.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.recent-practices .section-header .section-title.data-v-3f273c1e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.recent-practices .section-header .section-more.data-v-3f273c1e {
  font-size: 26rpx;
  color: #4A90E2;
}
.recent-practices .practice-list .practice-item.data-v-3f273c1e {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.recent-practices .practice-list .practice-item .practice-info.data-v-3f273c1e {
  flex: 1;
}
.recent-practices .practice-list .practice-item .practice-info .practice-category.data-v-3f273c1e {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.recent-practices .practice-list .practice-item .practice-info .practice-time.data-v-3f273c1e {
  font-size: 24rpx;
  color: #999;
}
.recent-practices .practice-list .practice-item .practice-result.data-v-3f273c1e {
  text-align: right;
}
.recent-practices .practice-list .practice-item .practice-result .score.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}
.recent-practices .practice-list .practice-item .practice-result .score.good.data-v-3f273c1e {
  color: #4CAF50;
}
.recent-practices .practice-list .practice-item .practice-result .score.normal.data-v-3f273c1e {
  color: #FF9500;
}
.recent-practices .practice-list .practice-item .practice-result .score.poor.data-v-3f273c1e {
  color: #f56c6c;
}
.recent-practices .practice-list .practice-item .practice-result .accuracy.data-v-3f273c1e {
  font-size: 24rpx;
  color: #999;
}