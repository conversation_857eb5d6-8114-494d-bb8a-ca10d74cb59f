/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-5dc513bd {
  box-sizing: border-box;
}
page.data-v-5dc513bd {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-5dc513bd {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-5dc513bd {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-5dc513bd {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-5dc513bd {
  display: flex;
}
.flex-column.data-v-5dc513bd {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-5dc513bd {
  flex: 1;
}
.flex-wrap.data-v-5dc513bd {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-5dc513bd {
  color: #262626;
}
.text-secondary.data-v-5dc513bd {
  color: #595959;
}
.text-disabled.data-v-5dc513bd {
  color: #BFBFBF;
}
.text-success.data-v-5dc513bd {
  color: #52C41A;
}
.text-warning.data-v-5dc513bd {
  color: #FAAD14;
}
.text-error.data-v-5dc513bd {
  color: #F5222D;
}
.text-primary-color.data-v-5dc513bd {
  color: #2E8B57;
}
.text-center.data-v-5dc513bd {
  text-align: center;
}
.text-left.data-v-5dc513bd {
  text-align: left;
}
.text-right.data-v-5dc513bd {
  text-align: right;
}
.text-bold.data-v-5dc513bd {
  font-weight: bold;
}
.text-normal.data-v-5dc513bd {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-5dc513bd {
  font-size: 20rpx;
}
.text-sm.data-v-5dc513bd {
  font-size: 24rpx;
}
.text-base.data-v-5dc513bd {
  font-size: 28rpx;
}
.text-lg.data-v-5dc513bd {
  font-size: 32rpx;
}
.text-xl.data-v-5dc513bd {
  font-size: 36rpx;
}
.text-2xl.data-v-5dc513bd {
  font-size: 40rpx;
}
.text-3xl.data-v-5dc513bd {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-5dc513bd {
  margin: 0;
}
.m-1.data-v-5dc513bd {
  margin: 8rpx;
}
.m-2.data-v-5dc513bd {
  margin: 16rpx;
}
.m-3.data-v-5dc513bd {
  margin: 24rpx;
}
.m-4.data-v-5dc513bd {
  margin: 32rpx;
}
.m-5.data-v-5dc513bd {
  margin: 40rpx;
}
.mt-0.data-v-5dc513bd {
  margin-top: 0;
}
.mt-1.data-v-5dc513bd {
  margin-top: 8rpx;
}
.mt-2.data-v-5dc513bd {
  margin-top: 16rpx;
}
.mt-3.data-v-5dc513bd {
  margin-top: 24rpx;
}
.mt-4.data-v-5dc513bd {
  margin-top: 32rpx;
}
.mt-5.data-v-5dc513bd {
  margin-top: 40rpx;
}
.mb-0.data-v-5dc513bd {
  margin-bottom: 0;
}
.mb-1.data-v-5dc513bd {
  margin-bottom: 8rpx;
}
.mb-2.data-v-5dc513bd {
  margin-bottom: 16rpx;
}
.mb-3.data-v-5dc513bd {
  margin-bottom: 24rpx;
}
.mb-4.data-v-5dc513bd {
  margin-bottom: 32rpx;
}
.mb-5.data-v-5dc513bd {
  margin-bottom: 40rpx;
}
.ml-0.data-v-5dc513bd {
  margin-left: 0;
}
.ml-1.data-v-5dc513bd {
  margin-left: 8rpx;
}
.ml-2.data-v-5dc513bd {
  margin-left: 16rpx;
}
.ml-3.data-v-5dc513bd {
  margin-left: 24rpx;
}
.ml-4.data-v-5dc513bd {
  margin-left: 32rpx;
}
.ml-5.data-v-5dc513bd {
  margin-left: 40rpx;
}
.mr-0.data-v-5dc513bd {
  margin-right: 0;
}
.mr-1.data-v-5dc513bd {
  margin-right: 8rpx;
}
.mr-2.data-v-5dc513bd {
  margin-right: 16rpx;
}
.mr-3.data-v-5dc513bd {
  margin-right: 24rpx;
}
.mr-4.data-v-5dc513bd {
  margin-right: 32rpx;
}
.mr-5.data-v-5dc513bd {
  margin-right: 40rpx;
}
.p-0.data-v-5dc513bd {
  padding: 0;
}
.p-1.data-v-5dc513bd {
  padding: 8rpx;
}
.p-2.data-v-5dc513bd {
  padding: 16rpx;
}
.p-3.data-v-5dc513bd {
  padding: 24rpx;
}
.p-4.data-v-5dc513bd {
  padding: 32rpx;
}
.p-5.data-v-5dc513bd {
  padding: 40rpx;
}
.pt-0.data-v-5dc513bd {
  padding-top: 0;
}
.pt-1.data-v-5dc513bd {
  padding-top: 8rpx;
}
.pt-2.data-v-5dc513bd {
  padding-top: 16rpx;
}
.pt-3.data-v-5dc513bd {
  padding-top: 24rpx;
}
.pt-4.data-v-5dc513bd {
  padding-top: 32rpx;
}
.pt-5.data-v-5dc513bd {
  padding-top: 40rpx;
}
.pb-0.data-v-5dc513bd {
  padding-bottom: 0;
}
.pb-1.data-v-5dc513bd {
  padding-bottom: 8rpx;
}
.pb-2.data-v-5dc513bd {
  padding-bottom: 16rpx;
}
.pb-3.data-v-5dc513bd {
  padding-bottom: 24rpx;
}
.pb-4.data-v-5dc513bd {
  padding-bottom: 32rpx;
}
.pb-5.data-v-5dc513bd {
  padding-bottom: 40rpx;
}
.pl-0.data-v-5dc513bd {
  padding-left: 0;
}
.pl-1.data-v-5dc513bd {
  padding-left: 8rpx;
}
.pl-2.data-v-5dc513bd {
  padding-left: 16rpx;
}
.pl-3.data-v-5dc513bd {
  padding-left: 24rpx;
}
.pl-4.data-v-5dc513bd {
  padding-left: 32rpx;
}
.pl-5.data-v-5dc513bd {
  padding-left: 40rpx;
}
.pr-0.data-v-5dc513bd {
  padding-right: 0;
}
.pr-1.data-v-5dc513bd {
  padding-right: 8rpx;
}
.pr-2.data-v-5dc513bd {
  padding-right: 16rpx;
}
.pr-3.data-v-5dc513bd {
  padding-right: 24rpx;
}
.pr-4.data-v-5dc513bd {
  padding-right: 32rpx;
}
.pr-5.data-v-5dc513bd {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-5dc513bd {
  width: 100%;
}
.h-full.data-v-5dc513bd {
  height: 100%;
}
.w-screen.data-v-5dc513bd {
  width: 100vw;
}
.h-screen.data-v-5dc513bd {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-5dc513bd {
  border-radius: 0;
}
.rounded-sm.data-v-5dc513bd {
  border-radius: 4rpx;
}
.rounded.data-v-5dc513bd {
  border-radius: 8rpx;
}
.rounded-lg.data-v-5dc513bd {
  border-radius: 16rpx;
}
.rounded-xl.data-v-5dc513bd {
  border-radius: 24rpx;
}
.rounded-full.data-v-5dc513bd {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-5dc513bd {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-5dc513bd {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-5dc513bd {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-5dc513bd {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-5dc513bd {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-5dc513bd {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-5dc513bd {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-5dc513bd {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-5dc513bd {
  background-color: #2E8B57;
}
.bg-light.data-v-5dc513bd {
  background-color: #FFFFFF;
}
.bg-gray.data-v-5dc513bd {
  background-color: #F8F9FA;
}
.bg-success.data-v-5dc513bd {
  background-color: #52C41A;
}
.bg-warning.data-v-5dc513bd {
  background-color: #FAAD14;
}
.bg-error.data-v-5dc513bd {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-5dc513bd {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-5dc513bd {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-5dc513bd {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-5dc513bd {
  color: #FAAD14;
}
.status-approved.data-v-5dc513bd {
  color: #52C41A;
}
.status-rejected.data-v-5dc513bd {
  color: #F5222D;
}
.status-not-submitted.data-v-5dc513bd {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-5dc513bd {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-5dc513bd {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-5dc513bd {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-5dc513bd {
  animation: fadeIn-5dc513bd 0.3s ease-in-out;
}
@keyframes fadeIn-5dc513bd {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-5dc513bd {
  animation: slideUp-5dc513bd 0.3s ease-out;
}
@keyframes slideUp-5dc513bd {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.summary-container.data-v-5dc513bd {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 120rpx;
}
.summary-content.data-v-5dc513bd {
  padding: 24rpx;
}
.score-card.data-v-5dc513bd {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  color: #fff;
}
.score-card .score-header.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}
.score-card .score-header .score-icon.data-v-5dc513bd {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 40rpx;
}
.score-card .score-header .score-icon.excellent.data-v-5dc513bd {
  background: #FFD700;
}
.score-card .score-header .score-icon.good.data-v-5dc513bd {
  background: #4CAF50;
}
.score-card .score-header .score-icon.normal.data-v-5dc513bd {
  background: #FF9500;
}
.score-card .score-header .score-icon.poor.data-v-5dc513bd {
  background: #f56c6c;
}
.score-card .score-header .score-info.data-v-5dc513bd {
  display: flex;
  align-items: baseline;
}
.score-card .score-header .score-info .score-number.data-v-5dc513bd {
  font-size: 80rpx;
  font-weight: bold;
}
.score-card .score-header .score-info .score-label.data-v-5dc513bd {
  font-size: 32rpx;
  margin-left: 8rpx;
}
.score-card .score-details.data-v-5dc513bd {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
}
.score-card .score-details .detail-item.data-v-5dc513bd {
  text-align: center;
}
.score-card .score-details .detail-item .detail-label.data-v-5dc513bd {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}
.score-card .score-details .detail-item .detail-value.data-v-5dc513bd {
  font-size: 32rpx;
  font-weight: bold;
}
.score-card .score-evaluation.data-v-5dc513bd {
  text-align: center;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
}
.score-card .score-evaluation .evaluation-text.data-v-5dc513bd {
  font-size: 28rpx;
  line-height: 1.4;
}
.answer-details.data-v-5dc513bd,
.study-suggestions.data-v-5dc513bd {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.answer-details .section-header.data-v-5dc513bd,
.study-suggestions .section-header.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.answer-details .section-header .section-title.data-v-5dc513bd,
.study-suggestions .section-header .section-title.data-v-5dc513bd {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-left: 16rpx;
}
.answer-details .section-header .filter-tabs.data-v-5dc513bd,
.study-suggestions .section-header .filter-tabs.data-v-5dc513bd {
  display: flex;
  gap: 16rpx;
}
.answer-details .section-header .filter-tabs .filter-tab.data-v-5dc513bd,
.study-suggestions .section-header .filter-tabs .filter-tab.data-v-5dc513bd {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
}
.answer-details .section-header .filter-tabs .filter-tab.active.data-v-5dc513bd,
.study-suggestions .section-header .filter-tabs .filter-tab.active.data-v-5dc513bd {
  background: #4A90E2;
  color: #fff;
}
.answer-details .section-header .filter-tabs .filter-tab text.data-v-5dc513bd,
.study-suggestions .section-header .filter-tabs .filter-tab text.data-v-5dc513bd {
  font-size: 24rpx;
}
.question-list .question-item.data-v-5dc513bd {
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.question-list .question-item .question-header.data-v-5dc513bd {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.question-list .question-item .question-header .question-number.data-v-5dc513bd {
  width: 60rpx;
  height: 60rpx;
  background: #4A90E2;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.question-list .question-item .question-header .question-number text.data-v-5dc513bd {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}
.question-list .question-item .question-content.data-v-5dc513bd {
  margin-bottom: 24rpx;
}
.question-list .question-item .question-content .question-title.data-v-5dc513bd {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  margin-bottom: 16rpx;
}
.question-list .question-item .question-content .question-meta.data-v-5dc513bd {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.question-list .question-item .question-content .question-meta .difficulty.data-v-5dc513bd {
  font-size: 24rpx;
  color: #666;
}
.question-list .question-item .answer-preview .user-answer.data-v-5dc513bd,
.question-list .question-item .answer-preview .correct-answer.data-v-5dc513bd {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.question-list .question-item .answer-preview .user-answer .answer-label.data-v-5dc513bd,
.question-list .question-item .answer-preview .correct-answer .answer-label.data-v-5dc513bd {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}
.question-list .question-item .answer-preview .user-answer .answer-value.data-v-5dc513bd,
.question-list .question-item .answer-preview .correct-answer .answer-value.data-v-5dc513bd {
  font-size: 26rpx;
}
.question-list .question-item .answer-preview .user-answer .answer-value.correct.data-v-5dc513bd,
.question-list .question-item .answer-preview .correct-answer .answer-value.correct.data-v-5dc513bd {
  color: #4CAF50;
}
.question-list .question-item .answer-preview .user-answer .answer-value.wrong.data-v-5dc513bd,
.question-list .question-item .answer-preview .correct-answer .answer-value.wrong.data-v-5dc513bd {
  color: #f56c6c;
}
.suggestion-list .suggestion-item.data-v-5dc513bd {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}
.suggestion-list .suggestion-item.data-v-5dc513bd:last-child {
  border-bottom: none;
}
.suggestion-list .suggestion-item .suggestion-icon.data-v-5dc513bd {
  margin-right: 24rpx;
}
.suggestion-list .suggestion-item .suggestion-content.data-v-5dc513bd {
  flex: 1;
}
.suggestion-list .suggestion-item .suggestion-content .suggestion-title.data-v-5dc513bd {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.suggestion-list .suggestion-item .suggestion-content .suggestion-desc.data-v-5dc513bd {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
.bottom-actions.data-v-5dc513bd {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 30rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
}
.bottom-actions .action-btn.data-v-5dc513bd {
  flex: 1;
}
.bottom-actions .action-btn.secondary.data-v-5dc513bd {
  flex: 1;
}
.bottom-actions .action-btn.primary.data-v-5dc513bd {
  flex: 1.5;
}
.question-detail-modal.data-v-5dc513bd {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.question-detail-modal .modal-header.data-v-5dc513bd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.question-detail-modal .modal-header .modal-title.data-v-5dc513bd {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.question-detail-modal .modal-content.data-v-5dc513bd {
  flex: 1;
  padding: 40rpx;
}
.question-detail-modal .detail-question.data-v-5dc513bd {
  margin-bottom: 40rpx;
}
.question-detail-modal .detail-question .question-info.data-v-5dc513bd {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.question-detail-modal .detail-question .question-info .question-number.data-v-5dc513bd {
  font-size: 24rpx;
  color: #666;
}
.question-detail-modal .detail-question .question-title.data-v-5dc513bd {
  font-size: 32rpx;
  line-height: 1.5;
  color: #333;
}
.question-detail-modal .detail-options.data-v-5dc513bd {
  margin-bottom: 40rpx;
}
.question-detail-modal .detail-options .detail-option.data-v-5dc513bd {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}
.question-detail-modal .detail-options .detail-option.user-correct.data-v-5dc513bd {
  background: rgba(76, 175, 80, 0.1);
  border-color: #4CAF50;
}
.question-detail-modal .detail-options .detail-option.user-wrong.data-v-5dc513bd {
  background: rgba(245, 108, 108, 0.1);
  border-color: #f56c6c;
}
.question-detail-modal .detail-options .detail-option.correct-answer.data-v-5dc513bd {
  background: rgba(76, 175, 80, 0.1);
  border-color: #4CAF50;
}
.question-detail-modal .detail-options .detail-option .option-label.data-v-5dc513bd {
  font-size: 28rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-right: 16rpx;
  min-width: 40rpx;
}
.question-detail-modal .detail-options .detail-option .option-text.data-v-5dc513bd {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
}
.question-detail-modal .explanation .explanation-header.data-v-5dc513bd {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.question-detail-modal .explanation .explanation-header .explanation-title.data-v-5dc513bd {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-left: 16rpx;
}
.question-detail-modal .explanation .explanation-content.data-v-5dc513bd {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
}