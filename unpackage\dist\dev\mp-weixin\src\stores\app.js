"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_constants_index = require("../constants/index.js");
const useAppStore = common_vendor.defineStore("app", () => {
  const loading = common_vendor.ref(false);
  const networkStatus = common_vendor.ref(true);
  const systemInfo = common_vendor.ref(null);
  const settings = common_vendor.ref({
    theme: "light",
    language: "zh-CN",
    enableNotifications: true,
    enableSounds: true,
    practiceReminder: true,
    examReminder: true
  });
  const setLoading = (status) => {
    loading.value = status;
  };
  const setNetworkStatus = (status) => {
    networkStatus.value = status;
  };
  const setSystemInfo = (info) => {
    systemInfo.value = info;
  };
  const getSystemInfo = () => __async(exports, null, function* () {
    try {
      const info = yield common_vendor.index.getSystemInfo();
      setSystemInfo(info[1]);
      return info[1];
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/app.ts:50", "Failed to get system info:", error);
      return null;
    }
  });
  const updateSettings = (newSettings) => {
    settings.value = __spreadValues(__spreadValues({}, settings.value), newSettings);
    saveSettings();
  };
  const saveSettings = () => {
    try {
      common_vendor.index.setStorageSync(src_constants_index.STORAGE_KEYS.SETTINGS, JSON.stringify(settings.value));
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/app.ts:66", "Failed to save settings:", error);
    }
  };
  const loadSettings = () => {
    try {
      const storedSettings = common_vendor.index.getStorageSync(src_constants_index.STORAGE_KEYS.SETTINGS);
      if (storedSettings) {
        settings.value = __spreadValues(__spreadValues({}, settings.value), JSON.parse(storedSettings));
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/app.ts:78", "Failed to load settings:", error);
    }
  };
  const showToast = (title, icon = "none") => {
    common_vendor.index.showToast({
      title,
      icon,
      duration: 2e3
    });
  };
  const showLoading = (title = "加载中...") => {
    setLoading(true);
    common_vendor.index.showLoading({ title });
  };
  const hideLoading = () => {
    setLoading(false);
    common_vendor.index.hideLoading();
  };
  const showModal = (options) => {
    return new Promise((resolve) => {
      common_vendor.index.showModal({
        title: options.title || "提示",
        content: options.content,
        showCancel: options.showCancel !== false,
        confirmText: options.confirmText || "确定",
        cancelText: options.cancelText || "取消",
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  };
  const navigateTo = (url, params) => {
    let fullUrl = url;
    if (params) {
      const query = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
      fullUrl += `?${query}`;
    }
    common_vendor.index.navigateTo({ url: fullUrl });
  };
  const redirectTo = (url, params) => {
    let fullUrl = url;
    if (params) {
      const query = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
      fullUrl += `?${query}`;
    }
    common_vendor.index.redirectTo({ url: fullUrl });
  };
  const switchTab = (url) => {
    common_vendor.index.switchTab({ url });
  };
  const navigateBack = (delta = 1) => {
    common_vendor.index.navigateBack({ delta });
  };
  const checkNetworkStatus = () => {
    common_vendor.index.getNetworkType({
      success: (res) => {
        setNetworkStatus(res.networkType !== "none");
      },
      fail: () => {
        setNetworkStatus(false);
      }
    });
  };
  const watchNetworkStatus = () => {
    common_vendor.index.onNetworkStatusChange((res) => {
      setNetworkStatus(res.isConnected);
    });
  };
  const initApp = () => __async(exports, null, function* () {
    loadSettings();
    yield getSystemInfo();
    checkNetworkStatus();
    watchNetworkStatus();
  });
  return {
    // 状态
    loading,
    networkStatus,
    systemInfo,
    settings,
    // 方法
    setLoading,
    setNetworkStatus,
    setSystemInfo,
    getSystemInfo,
    updateSettings,
    saveSettings,
    loadSettings,
    showToast,
    showLoading,
    hideLoading,
    showModal,
    navigateTo,
    redirectTo,
    switchTab,
    navigateBack,
    checkNetworkStatus,
    watchNetworkStatus,
    initApp
  };
});
exports.useAppStore = useAppStore;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/stores/app.js.map
