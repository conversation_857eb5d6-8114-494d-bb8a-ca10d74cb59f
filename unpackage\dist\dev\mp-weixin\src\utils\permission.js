"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const src_stores_user = require("../stores/user.js");
const src_stores_app = require("../stores/app.js");
const src_constants_index = require("../constants/index.js");
class PermissionManager {
  constructor() {
    this.userStore = src_stores_user.useUserStore();
    this.appStore = src_stores_app.useAppStore();
  }
  /**
   * 检查用户是否已登录
   */
  isLoggedIn() {
    return this.userStore.isLoggedIn;
  }
  /**
   * 检查用户是否已认证（通过审核）
   */
  isAuthenticated() {
    return this.userStore.isAuthenticated;
  }
  /**
   * 检查用户状态
   */
  getUserStatus() {
    var _a;
    return ((_a = this.userStore.userInfo) == null ? void 0 : _a.status) || null;
  }
  /**
   * 检查是否可以访问考试功能
   */
  canAccessExam() {
    return this.userStore.canAccessExam;
  }
  /**
   * 检查是否可以访问完整功能
   */
  canAccessFullFeatures() {
    return this.userStore.canAccessFullFeatures;
  }
  /**
   * 页面访问权限检查
   */
  checkPageAccess(pagePath) {
    const publicPages = [
      src_constants_index.PAGE_PATHS.LOGIN,
      src_constants_index.PAGE_PATHS.PROFILE
    ];
    if (publicPages.includes(pagePath)) {
      return { allowed: true };
    }
    if (!this.isLoggedIn()) {
      return {
        allowed: false,
        redirectTo: src_constants_index.PAGE_PATHS.LOGIN,
        message: "请先登录"
      };
    }
    const authRequiredPages = [
      src_constants_index.PAGE_PATHS.EXAM,
      src_constants_index.PAGE_PATHS.EXAM_ONLINE_READING,
      src_constants_index.PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,
      src_constants_index.PAGE_PATHS.EXAM_ONLINE_ANSWER,
      src_constants_index.PAGE_PATHS.EXAM_OFFLINE_DETAIL,
      src_constants_index.PAGE_PATHS.EXAM_HISTORY
    ];
    if (authRequiredPages.includes(pagePath)) {
      if (!this.isAuthenticated()) {
        return {
          allowed: false,
          redirectTo: src_constants_index.PAGE_PATHS.PERSONAL,
          message: "请先完善个人资料并通过审核"
        };
      }
    }
    const partialAccessPages = [
      src_constants_index.PAGE_PATHS.INFO,
      src_constants_index.PAGE_PATHS.STUDY,
      src_constants_index.PAGE_PATHS.PERSONAL
    ];
    if (partialAccessPages.includes(pagePath)) {
      return { allowed: true };
    }
    return { allowed: true };
  }
  /**
   * 功能权限检查
   */
  checkFeatureAccess(feature) {
    const userStatus = this.getUserStatus();
    switch (feature) {
      case "practice":
        return { allowed: true };
      case "exam":
        if (!this.isAuthenticated()) {
          return {
            allowed: false,
            message: "请先完善个人资料并通过机构审核后才能参加考试"
          };
        }
        return { allowed: true };
      case "certificate":
        if (!this.isAuthenticated()) {
          return {
            allowed: false,
            message: "请先完善个人资料并通过机构审核"
          };
        }
        return { allowed: true };
      case "profile_edit":
        if (userStatus === "pending" || userStatus === "approved") {
          return {
            allowed: false,
            message: "资料审核中或已通过审核，无法修改基本信息"
          };
        }
        return { allowed: true };
      case "unlimited_practice":
        if (!this.isAuthenticated()) {
          return {
            allowed: false,
            message: "认证用户可享受无限练习，请先完善资料并通过审核"
          };
        }
        return { allowed: true };
      default:
        return { allowed: true };
    }
  }
  /**
   * 执行权限检查并处理结果
   */
  enforcePageAccess(pagePath) {
    return __async(this, null, function* () {
      const result = this.checkPageAccess(pagePath);
      if (!result.allowed) {
        if (result.message) {
          this.appStore.showToast(result.message);
        }
        if (result.redirectTo) {
          setTimeout(() => {
            if (result.redirectTo === src_constants_index.PAGE_PATHS.LOGIN) {
              this.appStore.redirectTo(result.redirectTo);
            } else {
              this.appStore.switchTab(result.redirectTo);
            }
          }, 1500);
        }
        return false;
      }
      return true;
    });
  }
  /**
   * 执行功能权限检查
   */
  enforceFeatureAccess(feature) {
    const result = this.checkFeatureAccess(feature);
    if (!result.allowed && result.message) {
      this.appStore.showToast(result.message);
    }
    return result.allowed;
  }
  /**
   * 获取用户状态提示信息
   */
  getUserStatusMessage() {
    const userStatus = this.getUserStatus();
    switch (userStatus) {
      case "not_submitted":
        return "请完善个人资料以使用完整功能";
      case "pending":
        return "个人资料审核中，请耐心等待";
      case "approved":
        return "已通过认证，可使用所有功能";
      case "rejected":
        return "资料审核未通过，请重新提交";
      default:
        return "请先登录";
    }
  }
  /**
   * 获取功能限制提示
   */
  getFeatureLimitMessage(feature) {
    switch (feature) {
      case "practice":
        if (!this.isAuthenticated()) {
          return "未认证用户每日限制3次练习";
        }
        return "认证用户可无限练习";
      case "exam":
        return "需要通过机构审核后才能参加正式考试";
      case "certificate":
        return "需要通过考试后才能获得证书";
      default:
        return "";
    }
  }
}
const permissionManager = new PermissionManager();
exports.permissionManager = permissionManager;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/utils/permission.js.map
