/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-5d3ff4e0 {
  box-sizing: border-box;
}
page.data-v-5d3ff4e0 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-5d3ff4e0 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-5d3ff4e0 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-5d3ff4e0 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-5d3ff4e0 {
  display: flex;
}
.flex-column.data-v-5d3ff4e0 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-5d3ff4e0 {
  flex: 1;
}
.flex-wrap.data-v-5d3ff4e0 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-5d3ff4e0 {
  color: #262626;
}
.text-secondary.data-v-5d3ff4e0 {
  color: #595959;
}
.text-disabled.data-v-5d3ff4e0 {
  color: #BFBFBF;
}
.text-success.data-v-5d3ff4e0 {
  color: #52C41A;
}
.text-warning.data-v-5d3ff4e0 {
  color: #FAAD14;
}
.text-error.data-v-5d3ff4e0 {
  color: #F5222D;
}
.text-primary-color.data-v-5d3ff4e0 {
  color: #2E8B57;
}
.text-center.data-v-5d3ff4e0 {
  text-align: center;
}
.text-left.data-v-5d3ff4e0 {
  text-align: left;
}
.text-right.data-v-5d3ff4e0 {
  text-align: right;
}
.text-bold.data-v-5d3ff4e0 {
  font-weight: bold;
}
.text-normal.data-v-5d3ff4e0 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-5d3ff4e0 {
  font-size: 20rpx;
}
.text-sm.data-v-5d3ff4e0 {
  font-size: 24rpx;
}
.text-base.data-v-5d3ff4e0 {
  font-size: 28rpx;
}
.text-lg.data-v-5d3ff4e0 {
  font-size: 32rpx;
}
.text-xl.data-v-5d3ff4e0 {
  font-size: 36rpx;
}
.text-2xl.data-v-5d3ff4e0 {
  font-size: 40rpx;
}
.text-3xl.data-v-5d3ff4e0 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-5d3ff4e0 {
  margin: 0;
}
.m-1.data-v-5d3ff4e0 {
  margin: 8rpx;
}
.m-2.data-v-5d3ff4e0 {
  margin: 16rpx;
}
.m-3.data-v-5d3ff4e0 {
  margin: 24rpx;
}
.m-4.data-v-5d3ff4e0 {
  margin: 32rpx;
}
.m-5.data-v-5d3ff4e0 {
  margin: 40rpx;
}
.mt-0.data-v-5d3ff4e0 {
  margin-top: 0;
}
.mt-1.data-v-5d3ff4e0 {
  margin-top: 8rpx;
}
.mt-2.data-v-5d3ff4e0 {
  margin-top: 16rpx;
}
.mt-3.data-v-5d3ff4e0 {
  margin-top: 24rpx;
}
.mt-4.data-v-5d3ff4e0 {
  margin-top: 32rpx;
}
.mt-5.data-v-5d3ff4e0 {
  margin-top: 40rpx;
}
.mb-0.data-v-5d3ff4e0 {
  margin-bottom: 0;
}
.mb-1.data-v-5d3ff4e0 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-5d3ff4e0 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-5d3ff4e0 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-5d3ff4e0 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-5d3ff4e0 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-5d3ff4e0 {
  margin-left: 0;
}
.ml-1.data-v-5d3ff4e0 {
  margin-left: 8rpx;
}
.ml-2.data-v-5d3ff4e0 {
  margin-left: 16rpx;
}
.ml-3.data-v-5d3ff4e0 {
  margin-left: 24rpx;
}
.ml-4.data-v-5d3ff4e0 {
  margin-left: 32rpx;
}
.ml-5.data-v-5d3ff4e0 {
  margin-left: 40rpx;
}
.mr-0.data-v-5d3ff4e0 {
  margin-right: 0;
}
.mr-1.data-v-5d3ff4e0 {
  margin-right: 8rpx;
}
.mr-2.data-v-5d3ff4e0 {
  margin-right: 16rpx;
}
.mr-3.data-v-5d3ff4e0 {
  margin-right: 24rpx;
}
.mr-4.data-v-5d3ff4e0 {
  margin-right: 32rpx;
}
.mr-5.data-v-5d3ff4e0 {
  margin-right: 40rpx;
}
.p-0.data-v-5d3ff4e0 {
  padding: 0;
}
.p-1.data-v-5d3ff4e0 {
  padding: 8rpx;
}
.p-2.data-v-5d3ff4e0 {
  padding: 16rpx;
}
.p-3.data-v-5d3ff4e0 {
  padding: 24rpx;
}
.p-4.data-v-5d3ff4e0 {
  padding: 32rpx;
}
.p-5.data-v-5d3ff4e0 {
  padding: 40rpx;
}
.pt-0.data-v-5d3ff4e0 {
  padding-top: 0;
}
.pt-1.data-v-5d3ff4e0 {
  padding-top: 8rpx;
}
.pt-2.data-v-5d3ff4e0 {
  padding-top: 16rpx;
}
.pt-3.data-v-5d3ff4e0 {
  padding-top: 24rpx;
}
.pt-4.data-v-5d3ff4e0 {
  padding-top: 32rpx;
}
.pt-5.data-v-5d3ff4e0 {
  padding-top: 40rpx;
}
.pb-0.data-v-5d3ff4e0 {
  padding-bottom: 0;
}
.pb-1.data-v-5d3ff4e0 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-5d3ff4e0 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-5d3ff4e0 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-5d3ff4e0 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-5d3ff4e0 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-5d3ff4e0 {
  padding-left: 0;
}
.pl-1.data-v-5d3ff4e0 {
  padding-left: 8rpx;
}
.pl-2.data-v-5d3ff4e0 {
  padding-left: 16rpx;
}
.pl-3.data-v-5d3ff4e0 {
  padding-left: 24rpx;
}
.pl-4.data-v-5d3ff4e0 {
  padding-left: 32rpx;
}
.pl-5.data-v-5d3ff4e0 {
  padding-left: 40rpx;
}
.pr-0.data-v-5d3ff4e0 {
  padding-right: 0;
}
.pr-1.data-v-5d3ff4e0 {
  padding-right: 8rpx;
}
.pr-2.data-v-5d3ff4e0 {
  padding-right: 16rpx;
}
.pr-3.data-v-5d3ff4e0 {
  padding-right: 24rpx;
}
.pr-4.data-v-5d3ff4e0 {
  padding-right: 32rpx;
}
.pr-5.data-v-5d3ff4e0 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-5d3ff4e0 {
  width: 100%;
}
.h-full.data-v-5d3ff4e0 {
  height: 100%;
}
.w-screen.data-v-5d3ff4e0 {
  width: 100vw;
}
.h-screen.data-v-5d3ff4e0 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-5d3ff4e0 {
  border-radius: 0;
}
.rounded-sm.data-v-5d3ff4e0 {
  border-radius: 4rpx;
}
.rounded.data-v-5d3ff4e0 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-5d3ff4e0 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-5d3ff4e0 {
  border-radius: 24rpx;
}
.rounded-full.data-v-5d3ff4e0 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-5d3ff4e0 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-5d3ff4e0 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-5d3ff4e0 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-5d3ff4e0 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-5d3ff4e0 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-5d3ff4e0 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-5d3ff4e0 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-5d3ff4e0 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-5d3ff4e0 {
  background-color: #2E8B57;
}
.bg-light.data-v-5d3ff4e0 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-5d3ff4e0 {
  background-color: #F8F9FA;
}
.bg-success.data-v-5d3ff4e0 {
  background-color: #52C41A;
}
.bg-warning.data-v-5d3ff4e0 {
  background-color: #FAAD14;
}
.bg-error.data-v-5d3ff4e0 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-5d3ff4e0 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-5d3ff4e0 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-5d3ff4e0 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-5d3ff4e0 {
  color: #FAAD14;
}
.status-approved.data-v-5d3ff4e0 {
  color: #52C41A;
}
.status-rejected.data-v-5d3ff4e0 {
  color: #F5222D;
}
.status-not-submitted.data-v-5d3ff4e0 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-5d3ff4e0 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-5d3ff4e0 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-5d3ff4e0 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-5d3ff4e0 {
  animation: fadeIn-5d3ff4e0 0.3s ease-in-out;
}
@keyframes fadeIn-5d3ff4e0 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-5d3ff4e0 {
  animation: slideUp-5d3ff4e0 0.3s ease-out;
}
@keyframes slideUp-5d3ff4e0 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.offline-exam-container.data-v-5d3ff4e0 {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 120rpx;
}
.exam-content.data-v-5d3ff4e0 {
  padding: 24rpx;
}
.exam-info-card.data-v-5d3ff4e0,
.registration-status.data-v-5d3ff4e0,
.exam-notice.data-v-5d3ff4e0,
.contact-info.data-v-5d3ff4e0 {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.exam-info-card .exam-header.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.exam-info-card .exam-header .exam-title.data-v-5d3ff4e0 {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}
.exam-info-card .exam-description.data-v-5d3ff4e0 {
  margin-bottom: 32rpx;
}
.exam-info-card .exam-description text.data-v-5d3ff4e0 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.exam-info-card .exam-details .detail-row.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.exam-info-card .exam-details .detail-row.data-v-5d3ff4e0:last-child {
  margin-bottom: 0;
}
.exam-info-card .exam-details .detail-row .detail-label.data-v-5d3ff4e0 {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}
.exam-info-card .exam-details .detail-row .detail-value.data-v-5d3ff4e0 {
  font-size: 26rpx;
  font-weight: bold;
  color: #4A90E2;
}
.registration-status .status-header.data-v-5d3ff4e0,
.registration-status .notice-header.data-v-5d3ff4e0,
.registration-status .contact-header.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.registration-status .status-header .status-title.data-v-5d3ff4e0,
.registration-status .status-header .notice-title.data-v-5d3ff4e0,
.registration-status .status-header .contact-title.data-v-5d3ff4e0,
.registration-status .notice-header .status-title.data-v-5d3ff4e0,
.registration-status .notice-header .notice-title.data-v-5d3ff4e0,
.registration-status .notice-header .contact-title.data-v-5d3ff4e0,
.registration-status .contact-header .status-title.data-v-5d3ff4e0,
.registration-status .contact-header .notice-title.data-v-5d3ff4e0,
.registration-status .contact-header .contact-title.data-v-5d3ff4e0 {
  margin-left: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.registration-status .status-content .status-item.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.registration-status .status-content .status-item.data-v-5d3ff4e0:last-child {
  margin-bottom: 0;
}
.registration-status .status-content .status-item .status-label.data-v-5d3ff4e0 {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}
.registration-status .status-content .status-item .status-value.data-v-5d3ff4e0 {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}
.registration-status .no-registration.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: #fff7e6;
  border-radius: 16rpx;
}
.registration-status .no-registration .no-registration-text.data-v-5d3ff4e0 {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #FF9500;
}
.exam-notice .notice-content .notice-item.data-v-5d3ff4e0 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.exam-notice .notice-content .notice-item.data-v-5d3ff4e0:last-child {
  margin-bottom: 0;
}
.exam-notice .notice-content .notice-item .notice-number.data-v-5d3ff4e0 {
  width: 40rpx;
  height: 40rpx;
  background: #4A90E2;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  color: #fff;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.exam-notice .notice-content .notice-item .notice-text.data-v-5d3ff4e0 {
  flex: 1;
  font-size: 26rpx;
  line-height: 1.5;
  color: #333;
}
.contact-info .contact-content .contact-item.data-v-5d3ff4e0 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.contact-info .contact-content .contact-item.data-v-5d3ff4e0:last-child {
  margin-bottom: 0;
}
.contact-info .contact-content .contact-item .contact-text.data-v-5d3ff4e0 {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #333;
}
.bottom-actions.data-v-5d3ff4e0 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.bottom-actions .register-btn.data-v-5d3ff4e0,
.bottom-actions .cancel-btn.data-v-5d3ff4e0,
.bottom-actions .result-btn.data-v-5d3ff4e0 {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.bottom-actions .status-tip.data-v-5d3ff4e0 {
  text-align: center;
  padding: 24rpx;
}
.bottom-actions .status-tip text.data-v-5d3ff4e0 {
  font-size: 28rpx;
  color: #666;
}