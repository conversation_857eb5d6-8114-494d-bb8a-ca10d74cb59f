/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-d86fbda3 {
  box-sizing: border-box;
}
page.data-v-d86fbda3 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-d86fbda3 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-d86fbda3 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-d86fbda3 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-d86fbda3 {
  display: flex;
}
.flex-column.data-v-d86fbda3 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-d86fbda3 {
  flex: 1;
}
.flex-wrap.data-v-d86fbda3 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-d86fbda3 {
  color: #262626;
}
.text-secondary.data-v-d86fbda3 {
  color: #595959;
}
.text-disabled.data-v-d86fbda3 {
  color: #BFBFBF;
}
.text-success.data-v-d86fbda3 {
  color: #52C41A;
}
.text-warning.data-v-d86fbda3 {
  color: #FAAD14;
}
.text-error.data-v-d86fbda3 {
  color: #F5222D;
}
.text-primary-color.data-v-d86fbda3 {
  color: #2E8B57;
}
.text-center.data-v-d86fbda3 {
  text-align: center;
}
.text-left.data-v-d86fbda3 {
  text-align: left;
}
.text-right.data-v-d86fbda3 {
  text-align: right;
}
.text-bold.data-v-d86fbda3 {
  font-weight: bold;
}
.text-normal.data-v-d86fbda3 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-d86fbda3 {
  font-size: 20rpx;
}
.text-sm.data-v-d86fbda3 {
  font-size: 24rpx;
}
.text-base.data-v-d86fbda3 {
  font-size: 28rpx;
}
.text-lg.data-v-d86fbda3 {
  font-size: 32rpx;
}
.text-xl.data-v-d86fbda3 {
  font-size: 36rpx;
}
.text-2xl.data-v-d86fbda3 {
  font-size: 40rpx;
}
.text-3xl.data-v-d86fbda3 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-d86fbda3 {
  margin: 0;
}
.m-1.data-v-d86fbda3 {
  margin: 8rpx;
}
.m-2.data-v-d86fbda3 {
  margin: 16rpx;
}
.m-3.data-v-d86fbda3 {
  margin: 24rpx;
}
.m-4.data-v-d86fbda3 {
  margin: 32rpx;
}
.m-5.data-v-d86fbda3 {
  margin: 40rpx;
}
.mt-0.data-v-d86fbda3 {
  margin-top: 0;
}
.mt-1.data-v-d86fbda3 {
  margin-top: 8rpx;
}
.mt-2.data-v-d86fbda3 {
  margin-top: 16rpx;
}
.mt-3.data-v-d86fbda3 {
  margin-top: 24rpx;
}
.mt-4.data-v-d86fbda3 {
  margin-top: 32rpx;
}
.mt-5.data-v-d86fbda3 {
  margin-top: 40rpx;
}
.mb-0.data-v-d86fbda3 {
  margin-bottom: 0;
}
.mb-1.data-v-d86fbda3 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-d86fbda3 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-d86fbda3 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-d86fbda3 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-d86fbda3 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-d86fbda3 {
  margin-left: 0;
}
.ml-1.data-v-d86fbda3 {
  margin-left: 8rpx;
}
.ml-2.data-v-d86fbda3 {
  margin-left: 16rpx;
}
.ml-3.data-v-d86fbda3 {
  margin-left: 24rpx;
}
.ml-4.data-v-d86fbda3 {
  margin-left: 32rpx;
}
.ml-5.data-v-d86fbda3 {
  margin-left: 40rpx;
}
.mr-0.data-v-d86fbda3 {
  margin-right: 0;
}
.mr-1.data-v-d86fbda3 {
  margin-right: 8rpx;
}
.mr-2.data-v-d86fbda3 {
  margin-right: 16rpx;
}
.mr-3.data-v-d86fbda3 {
  margin-right: 24rpx;
}
.mr-4.data-v-d86fbda3 {
  margin-right: 32rpx;
}
.mr-5.data-v-d86fbda3 {
  margin-right: 40rpx;
}
.p-0.data-v-d86fbda3 {
  padding: 0;
}
.p-1.data-v-d86fbda3 {
  padding: 8rpx;
}
.p-2.data-v-d86fbda3 {
  padding: 16rpx;
}
.p-3.data-v-d86fbda3 {
  padding: 24rpx;
}
.p-4.data-v-d86fbda3 {
  padding: 32rpx;
}
.p-5.data-v-d86fbda3 {
  padding: 40rpx;
}
.pt-0.data-v-d86fbda3 {
  padding-top: 0;
}
.pt-1.data-v-d86fbda3 {
  padding-top: 8rpx;
}
.pt-2.data-v-d86fbda3 {
  padding-top: 16rpx;
}
.pt-3.data-v-d86fbda3 {
  padding-top: 24rpx;
}
.pt-4.data-v-d86fbda3 {
  padding-top: 32rpx;
}
.pt-5.data-v-d86fbda3 {
  padding-top: 40rpx;
}
.pb-0.data-v-d86fbda3 {
  padding-bottom: 0;
}
.pb-1.data-v-d86fbda3 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-d86fbda3 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-d86fbda3 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-d86fbda3 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-d86fbda3 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-d86fbda3 {
  padding-left: 0;
}
.pl-1.data-v-d86fbda3 {
  padding-left: 8rpx;
}
.pl-2.data-v-d86fbda3 {
  padding-left: 16rpx;
}
.pl-3.data-v-d86fbda3 {
  padding-left: 24rpx;
}
.pl-4.data-v-d86fbda3 {
  padding-left: 32rpx;
}
.pl-5.data-v-d86fbda3 {
  padding-left: 40rpx;
}
.pr-0.data-v-d86fbda3 {
  padding-right: 0;
}
.pr-1.data-v-d86fbda3 {
  padding-right: 8rpx;
}
.pr-2.data-v-d86fbda3 {
  padding-right: 16rpx;
}
.pr-3.data-v-d86fbda3 {
  padding-right: 24rpx;
}
.pr-4.data-v-d86fbda3 {
  padding-right: 32rpx;
}
.pr-5.data-v-d86fbda3 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-d86fbda3 {
  width: 100%;
}
.h-full.data-v-d86fbda3 {
  height: 100%;
}
.w-screen.data-v-d86fbda3 {
  width: 100vw;
}
.h-screen.data-v-d86fbda3 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-d86fbda3 {
  border-radius: 0;
}
.rounded-sm.data-v-d86fbda3 {
  border-radius: 4rpx;
}
.rounded.data-v-d86fbda3 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-d86fbda3 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-d86fbda3 {
  border-radius: 24rpx;
}
.rounded-full.data-v-d86fbda3 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-d86fbda3 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-d86fbda3 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-d86fbda3 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-d86fbda3 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-d86fbda3 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-d86fbda3 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-d86fbda3 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-d86fbda3 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-d86fbda3 {
  background-color: #2E8B57;
}
.bg-light.data-v-d86fbda3 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-d86fbda3 {
  background-color: #F8F9FA;
}
.bg-success.data-v-d86fbda3 {
  background-color: #52C41A;
}
.bg-warning.data-v-d86fbda3 {
  background-color: #FAAD14;
}
.bg-error.data-v-d86fbda3 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-d86fbda3 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-d86fbda3 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-d86fbda3 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-d86fbda3 {
  color: #FAAD14;
}
.status-approved.data-v-d86fbda3 {
  color: #52C41A;
}
.status-rejected.data-v-d86fbda3 {
  color: #F5222D;
}
.status-not-submitted.data-v-d86fbda3 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-d86fbda3 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-d86fbda3 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-d86fbda3 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-d86fbda3 {
  animation: fadeIn-d86fbda3 0.3s ease-in-out;
}
@keyframes fadeIn-d86fbda3 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-d86fbda3 {
  animation: slideUp-d86fbda3 0.3s ease-out;
}
@keyframes slideUp-d86fbda3 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.about-container.data-v-d86fbda3 {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 40rpx;
}
.app-info-section.data-v-d86fbda3 {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.app-info-section .app-logo.data-v-d86fbda3 {
  margin-bottom: 32rpx;
}
.app-info-section .app-logo .logo-image.data-v-d86fbda3 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
}
.app-info-section .app-details .app-name.data-v-d86fbda3 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.app-info-section .app-details .app-version.data-v-d86fbda3 {
  display: block;
  font-size: 26rpx;
  color: #2E8B57;
  margin-bottom: 16rpx;
}
.app-info-section .app-details .app-description.data-v-d86fbda3 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.features-section.data-v-d86fbda3,
.support-section.data-v-d86fbda3,
.team-section.data-v-d86fbda3,
.legal-section.data-v-d86fbda3,
.update-section.data-v-d86fbda3 {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.features-section .section-header.data-v-d86fbda3,
.support-section .section-header.data-v-d86fbda3,
.team-section .section-header.data-v-d86fbda3,
.legal-section .section-header.data-v-d86fbda3,
.update-section .section-header.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.features-section .section-header .section-title.data-v-d86fbda3,
.support-section .section-header .section-title.data-v-d86fbda3,
.team-section .section-header .section-title.data-v-d86fbda3,
.legal-section .section-header .section-title.data-v-d86fbda3,
.update-section .section-header .section-title.data-v-d86fbda3 {
  margin-left: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.features-list .feature-item.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.features-list .feature-item.data-v-d86fbda3:last-child {
  margin-bottom: 0;
}
.features-list .feature-item .feature-icon.data-v-d86fbda3 {
  width: 80rpx;
  height: 80rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.features-list .feature-item .feature-content.data-v-d86fbda3 {
  flex: 1;
}
.features-list .feature-item .feature-content .feature-title.data-v-d86fbda3 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.features-list .feature-item .feature-content .feature-desc.data-v-d86fbda3 {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
.support-list .support-item.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}
.support-list .support-item.data-v-d86fbda3:last-child {
  border-bottom: none;
}
.support-list .support-item .support-icon.data-v-d86fbda3 {
  margin-right: 24rpx;
}
.support-list .support-item .support-content.data-v-d86fbda3 {
  flex: 1;
}
.support-list .support-item .support-content .support-title.data-v-d86fbda3 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}
.support-list .support-item .support-content .support-value.data-v-d86fbda3 {
  font-size: 24rpx;
  color: #666;
}
.team-content .team-description.data-v-d86fbda3 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32rpx;
}
.team-content .tech-stack .tech-title.data-v-d86fbda3 {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.team-content .tech-stack .tech-tags.data-v-d86fbda3 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.team-content .tech-stack .tech-tags .tech-tag.data-v-d86fbda3 {
  padding: 8rpx 16rpx;
  background: #e3f2fd;
  color: #2E8B57;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: bold;
}
.legal-list .legal-item.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}
.legal-list .legal-item.data-v-d86fbda3:last-child {
  border-bottom: none;
}
.legal-list .legal-item .legal-title.data-v-d86fbda3 {
  font-size: 28rpx;
  color: #333;
}
.legal-list .legal-item .legal-value.data-v-d86fbda3 {
  font-size: 24rpx;
  color: #666;
}
.update-content .version-info.data-v-d86fbda3 {
  margin-bottom: 32rpx;
}
.update-content .version-info .version-item.data-v-d86fbda3 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.update-content .version-info .version-item.data-v-d86fbda3:last-child {
  margin-bottom: 0;
}
.update-content .version-info .version-item .version-label.data-v-d86fbda3 {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}
.update-content .version-info .version-item .version-value.data-v-d86fbda3 {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}
.update-content .check-update-btn.data-v-d86fbda3 {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
}
.footer-info.data-v-d86fbda3 {
  text-align: center;
  padding: 40rpx;
}
.footer-info .footer-text.data-v-d86fbda3 {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.footer-info .footer-text.data-v-d86fbda3:last-child {
  margin-bottom: 0;
}