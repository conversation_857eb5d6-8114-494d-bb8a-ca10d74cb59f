/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-4d59f12d {
  box-sizing: border-box;
}
page.data-v-4d59f12d {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-4d59f12d {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-4d59f12d {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-4d59f12d {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-4d59f12d {
  display: flex;
}
.flex-column.data-v-4d59f12d {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-4d59f12d {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-4d59f12d {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-4d59f12d {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-4d59f12d {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-4d59f12d {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-4d59f12d {
  flex: 1;
}
.flex-wrap.data-v-4d59f12d {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-4d59f12d {
  color: #262626;
}
.text-secondary.data-v-4d59f12d {
  color: #595959;
}
.text-disabled.data-v-4d59f12d {
  color: #BFBFBF;
}
.text-success.data-v-4d59f12d {
  color: #52C41A;
}
.text-warning.data-v-4d59f12d {
  color: #FAAD14;
}
.text-error.data-v-4d59f12d {
  color: #F5222D;
}
.text-primary-color.data-v-4d59f12d {
  color: #2E8B57;
}
.text-center.data-v-4d59f12d {
  text-align: center;
}
.text-left.data-v-4d59f12d {
  text-align: left;
}
.text-right.data-v-4d59f12d {
  text-align: right;
}
.text-bold.data-v-4d59f12d {
  font-weight: bold;
}
.text-normal.data-v-4d59f12d {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-4d59f12d {
  font-size: 20rpx;
}
.text-sm.data-v-4d59f12d {
  font-size: 24rpx;
}
.text-base.data-v-4d59f12d {
  font-size: 28rpx;
}
.text-lg.data-v-4d59f12d {
  font-size: 32rpx;
}
.text-xl.data-v-4d59f12d {
  font-size: 36rpx;
}
.text-2xl.data-v-4d59f12d {
  font-size: 40rpx;
}
.text-3xl.data-v-4d59f12d {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-4d59f12d {
  margin: 0;
}
.m-1.data-v-4d59f12d {
  margin: 8rpx;
}
.m-2.data-v-4d59f12d {
  margin: 16rpx;
}
.m-3.data-v-4d59f12d {
  margin: 24rpx;
}
.m-4.data-v-4d59f12d {
  margin: 32rpx;
}
.m-5.data-v-4d59f12d {
  margin: 40rpx;
}
.mt-0.data-v-4d59f12d {
  margin-top: 0;
}
.mt-1.data-v-4d59f12d {
  margin-top: 8rpx;
}
.mt-2.data-v-4d59f12d {
  margin-top: 16rpx;
}
.mt-3.data-v-4d59f12d {
  margin-top: 24rpx;
}
.mt-4.data-v-4d59f12d {
  margin-top: 32rpx;
}
.mt-5.data-v-4d59f12d {
  margin-top: 40rpx;
}
.mb-0.data-v-4d59f12d {
  margin-bottom: 0;
}
.mb-1.data-v-4d59f12d {
  margin-bottom: 8rpx;
}
.mb-2.data-v-4d59f12d {
  margin-bottom: 16rpx;
}
.mb-3.data-v-4d59f12d {
  margin-bottom: 24rpx;
}
.mb-4.data-v-4d59f12d {
  margin-bottom: 32rpx;
}
.mb-5.data-v-4d59f12d {
  margin-bottom: 40rpx;
}
.ml-0.data-v-4d59f12d {
  margin-left: 0;
}
.ml-1.data-v-4d59f12d {
  margin-left: 8rpx;
}
.ml-2.data-v-4d59f12d {
  margin-left: 16rpx;
}
.ml-3.data-v-4d59f12d {
  margin-left: 24rpx;
}
.ml-4.data-v-4d59f12d {
  margin-left: 32rpx;
}
.ml-5.data-v-4d59f12d {
  margin-left: 40rpx;
}
.mr-0.data-v-4d59f12d {
  margin-right: 0;
}
.mr-1.data-v-4d59f12d {
  margin-right: 8rpx;
}
.mr-2.data-v-4d59f12d {
  margin-right: 16rpx;
}
.mr-3.data-v-4d59f12d {
  margin-right: 24rpx;
}
.mr-4.data-v-4d59f12d {
  margin-right: 32rpx;
}
.mr-5.data-v-4d59f12d {
  margin-right: 40rpx;
}
.p-0.data-v-4d59f12d {
  padding: 0;
}
.p-1.data-v-4d59f12d {
  padding: 8rpx;
}
.p-2.data-v-4d59f12d {
  padding: 16rpx;
}
.p-3.data-v-4d59f12d {
  padding: 24rpx;
}
.p-4.data-v-4d59f12d {
  padding: 32rpx;
}
.p-5.data-v-4d59f12d {
  padding: 40rpx;
}
.pt-0.data-v-4d59f12d {
  padding-top: 0;
}
.pt-1.data-v-4d59f12d {
  padding-top: 8rpx;
}
.pt-2.data-v-4d59f12d {
  padding-top: 16rpx;
}
.pt-3.data-v-4d59f12d {
  padding-top: 24rpx;
}
.pt-4.data-v-4d59f12d {
  padding-top: 32rpx;
}
.pt-5.data-v-4d59f12d {
  padding-top: 40rpx;
}
.pb-0.data-v-4d59f12d {
  padding-bottom: 0;
}
.pb-1.data-v-4d59f12d {
  padding-bottom: 8rpx;
}
.pb-2.data-v-4d59f12d {
  padding-bottom: 16rpx;
}
.pb-3.data-v-4d59f12d {
  padding-bottom: 24rpx;
}
.pb-4.data-v-4d59f12d {
  padding-bottom: 32rpx;
}
.pb-5.data-v-4d59f12d {
  padding-bottom: 40rpx;
}
.pl-0.data-v-4d59f12d {
  padding-left: 0;
}
.pl-1.data-v-4d59f12d {
  padding-left: 8rpx;
}
.pl-2.data-v-4d59f12d {
  padding-left: 16rpx;
}
.pl-3.data-v-4d59f12d {
  padding-left: 24rpx;
}
.pl-4.data-v-4d59f12d {
  padding-left: 32rpx;
}
.pl-5.data-v-4d59f12d {
  padding-left: 40rpx;
}
.pr-0.data-v-4d59f12d {
  padding-right: 0;
}
.pr-1.data-v-4d59f12d {
  padding-right: 8rpx;
}
.pr-2.data-v-4d59f12d {
  padding-right: 16rpx;
}
.pr-3.data-v-4d59f12d {
  padding-right: 24rpx;
}
.pr-4.data-v-4d59f12d {
  padding-right: 32rpx;
}
.pr-5.data-v-4d59f12d {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-4d59f12d {
  width: 100%;
}
.h-full.data-v-4d59f12d {
  height: 100%;
}
.w-screen.data-v-4d59f12d {
  width: 100vw;
}
.h-screen.data-v-4d59f12d {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-4d59f12d {
  border-radius: 0;
}
.rounded-sm.data-v-4d59f12d {
  border-radius: 4rpx;
}
.rounded.data-v-4d59f12d {
  border-radius: 8rpx;
}
.rounded-lg.data-v-4d59f12d {
  border-radius: 16rpx;
}
.rounded-xl.data-v-4d59f12d {
  border-radius: 24rpx;
}
.rounded-full.data-v-4d59f12d {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-4d59f12d {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-4d59f12d {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-4d59f12d {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-4d59f12d {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-4d59f12d {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-4d59f12d {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-4d59f12d {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-4d59f12d {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-4d59f12d {
  background-color: #2E8B57;
}
.bg-light.data-v-4d59f12d {
  background-color: #FFFFFF;
}
.bg-gray.data-v-4d59f12d {
  background-color: #F8F9FA;
}
.bg-success.data-v-4d59f12d {
  background-color: #52C41A;
}
.bg-warning.data-v-4d59f12d {
  background-color: #FAAD14;
}
.bg-error.data-v-4d59f12d {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-4d59f12d {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-4d59f12d {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-4d59f12d {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-4d59f12d {
  color: #FAAD14;
}
.status-approved.data-v-4d59f12d {
  color: #52C41A;
}
.status-rejected.data-v-4d59f12d {
  color: #F5222D;
}
.status-not-submitted.data-v-4d59f12d {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-4d59f12d {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-4d59f12d {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-4d59f12d {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-4d59f12d {
  animation: fadeIn-4d59f12d 0.3s ease-in-out;
}
@keyframes fadeIn-4d59f12d {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-4d59f12d {
  animation: slideUp-4d59f12d 0.3s ease-out;
}
@keyframes slideUp-4d59f12d {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.personal-info-container.data-v-4d59f12d {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 120rpx;
}
.info-form.data-v-4d59f12d {
  padding: 24rpx;
}
.form-section.data-v-4d59f12d {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.form-section .section-title.data-v-4d59f12d {
  margin-bottom: 32rpx;
}
.form-section .section-title text.data-v-4d59f12d {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.avatar-upload.data-v-4d59f12d {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar-upload .avatar-container.data-v-4d59f12d {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 100rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}
.avatar-upload .avatar-container .avatar-image.data-v-4d59f12d {
  width: 100%;
  height: 100%;
}
.avatar-upload .avatar-container .avatar-overlay.data-v-4d59f12d {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.avatar-upload .avatar-container .avatar-overlay .upload-text.data-v-4d59f12d {
  margin-top: 8rpx;
  font-size: 20rpx;
  color: #fff;
}
.avatar-upload .avatar-container:active .avatar-overlay.data-v-4d59f12d {
  opacity: 1;
}
.avatar-upload .avatar-tips text.data-v-4d59f12d {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.form-items .form-item.data-v-4d59f12d {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}
.form-items .form-item.data-v-4d59f12d:last-child {
  border-bottom: none;
}
.form-items .form-item .item-label.data-v-4d59f12d {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.status-info .status-item.data-v-4d59f12d {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.status-info .status-item.data-v-4d59f12d:last-child {
  margin-bottom: 0;
}
.status-info .status-item .status-label.data-v-4d59f12d {
  width: 160rpx;
  font-size: 26rpx;
  color: #666;
}
.status-info .status-item .status-value.data-v-4d59f12d {
  font-size: 26rpx;
  color: #333;
}
.status-info .status-item .reject-reason.data-v-4d59f12d {
  flex: 1;
  font-size: 26rpx;
  color: #f56c6c;
  line-height: 1.5;
}
.bottom-actions.data-v-4d59f12d {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.bottom-actions .save-btn.data-v-4d59f12d,
.bottom-actions .resubmit-btn.data-v-4d59f12d {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.bottom-actions .readonly-tip.data-v-4d59f12d {
  text-align: center;
  padding: 24rpx;
}
.bottom-actions .readonly-tip text.data-v-4d59f12d {
  font-size: 28rpx;
  color: #666;
}